package corp.jamaro.jamaroservidor.app.service.file;

import io.minio.*;
import io.minio.errors.MinioException;
import io.minio.http.Method;
import io.minio.messages.Item;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.io.InputStream;
import java.util.concurrent.TimeUnit;

/**
 * Servicio para interactuar directamente con MinIO usando el SDK oficial.
 * Se encarga de:
 *  - Generar URLs prefirmadas.
 *  - Subir/descargar/eliminar objetos.
 *  - Consultar la metadata de un objeto (ETag, tamaño, etc.).
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MinioService {

    @Value("${minio.url}")
    private String minioUrl;

    @Value("${minio.access-key}")
    private String accessKey;

    @Value("${minio.secret-key}")
    private String secretKey;

    /**
     * Puedes inyectar el bucket por defecto si quieres
     * (aunque podrías usar uno por metodo).
     */
    @Value("${minio.bucket}")
    private String defaultBucket;

    // -------------------------------------------------------------------------
    //                    Métodos para obtener Presigned URLs
    // -------------------------------------------------------------------------

    /**
     * Genera una URL prefirmada para subir (PUT) un objeto.
     *
     * @param objectName Nombre del objeto en el bucket (ej: "folder/image.jpg").
     * @param expireSeconds Tiempo en segundos tras el cual expira la URL.
     * @param bucketName Opcional. Si null, usa defaultBucket.
     * @return La URL presignada (String).
     */
    public String getPresignedUrlForUpload(String objectName, long expireSeconds, String bucketName) throws MinioException {
        String bucket = (bucketName != null) ? bucketName : defaultBucket;
        try {
            return getClient().getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.PUT)
                            .bucket(bucket)
                            .object(objectName)
                            .expiry((int) expireSeconds, TimeUnit.SECONDS)
                            .build()
            );
        } catch (Exception e) {
            log.error("Error generando presigned URL para upload: {}", e.getMessage());
            throw new MinioException("Error al generar URL prefirmada (PUT). " + e.getMessage());
        }
    }

    /**
     * Genera una URL prefirmada para descargar (GET) un objeto.
     *
     * @param objectName Nombre del objeto.
     * @param expireSeconds Tiempo en segundos tras el cual expira la URL.
     * @param bucketName Opcional. Si null, usa defaultBucket.
     * @return La URL presignada (String).
     */
    public String getPresignedUrlForDownload(String objectName, long expireSeconds, String bucketName) throws MinioException {
        String bucket = (bucketName != null) ? bucketName : defaultBucket;
        try {
            return getClient().getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucket)
                            .object(objectName)
                            .expiry((int) expireSeconds, TimeUnit.SECONDS)
                            .build()
            );
        } catch (Exception e) {
            log.error("Error generando presigned URL para download: {}", e.getMessage());
            throw new MinioException("Error al generar URL prefirmada (GET). " + e.getMessage());
        }
    }

    // -------------------------------------------------------------------------
    //                    Métodos de subida/descarga directos
    // -------------------------------------------------------------------------

    /**
     * Sube un objeto al bucket de MinIO directamente, sin presigned URL (PUT).
     * Devuelve el ETag generado por MinIO.
     *
     * @param objectName Ruta y nombre del objeto en el bucket.
     * @param inputStream Contenido a subir.
     * @param size Tamaño en bytes. Si no lo sabes, puedes poner -1 (pero con limitación de chunk).
     * @param contentType MIME type (opcional).
     * @param bucketName Opcional. Si null, se usa defaultBucket.
     * @return ETag del objeto subido.
     */
    public String putObject(String objectName, InputStream inputStream, long size, String contentType, String bucketName) throws MinioException {
        String bucket = (bucketName != null) ? bucketName : defaultBucket;
        try {
            ObjectWriteResponse response = getClient().putObject(
                    PutObjectArgs.builder()
                            .bucket(bucket)
                            .object(objectName)
                            .stream(inputStream, size, -1) // -1 => tamaño desconocido
                            .contentType(contentType == null ? "application/octet-stream" : contentType)
                            .build()
            );
            return response.etag(); // ETag del objeto subido
        } catch (Exception e) {
            log.error("Error subiendo objeto a MinIO: {}", e.getMessage());
            throw new MinioException("Error al subir objeto: " + e.getMessage());
        }
    }

    /**
     * Versión reactiva (ejemplo) que recibe un Flux<DataBuffer> en vez de InputStream.
     * Típico si recibimos el archivo binario desde un controlador RSocket o WebFlux.
     */
    public String putObjectReactive(String objectName, Flux<DataBuffer> dataBufferFlux, String contentType, String bucketName) throws MinioException {
        // NOTA:
        //   Convertir un Flux<DataBuffer> a InputStream no es trivial.
        //   Lo usual es colectar DataBuffer en un ByteArrayOutputStream,
        //   o usar un Reactive wrapper. Aquí te dejamos la idea general.
        //   Ten en cuenta que puede requerir mucha memoria si el archivo es grande.
        throw new UnsupportedOperationException("Implementación personalizada para entornos reactivos");
    }

    /**
     * Descarga un objeto del bucket (bloqueante). Retorna un InputStream para leer el contenido.
     *
     * @param objectName Nombre del objeto.
     * @param bucketName Opcional. Si null, usa defaultBucket.
     * @return InputStream con el contenido.
     */
    public InputStream getObject(String objectName, String bucketName) throws MinioException {
        String bucket = (bucketName != null) ? bucketName : defaultBucket;
        try {
            return getClient().getObject(
                    GetObjectArgs.builder()
                            .bucket(bucket)
                            .object(objectName)
                            .build()
            );
        } catch (Exception e) {
            log.error("Error descargando objeto de MinIO: {}", e.getMessage());
            throw new MinioException("Error al descargar objeto: " + e.getMessage());
        }
    }

    // -------------------------------------------------------------------------
    //                          Eliminar objeto
    // -------------------------------------------------------------------------

    /**
     * Elimina un objeto del bucket.
     *
     * @param objectName Nombre del objeto.
     * @param bucketName Opcional. Si null, usa defaultBucket.
     */
    public void removeObject(String objectName, String bucketName) throws MinioException {
        String bucket = (bucketName != null) ? bucketName : defaultBucket;
        try {
            getClient().removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucket)
                            .object(objectName)
                            .build()
            );
        } catch (Exception e) {
            log.error("Error al eliminar objeto en MinIO: {}", e.getMessage());
            throw new MinioException("Error al eliminar objeto: " + e.getMessage());
        }
    }

    // -------------------------------------------------------------------------
    //                          Obtener metadata (StatObject)
    // -------------------------------------------------------------------------

    /**
     * Consulta metadatos de un objeto (tamaño, ETag, fecha de modificación, etc.)
     *
     * @param objectName Nombre del objeto.
     * @param bucketName Opcional. Si null, usa defaultBucket.
     * @return StatObjectResponse con la info del objeto.
     */
    public StatObjectResponse statObject(String objectName, String bucketName) throws MinioException {
        String bucket = (bucketName != null) ? bucketName : defaultBucket;
        try {
            return getClient().statObject(
                    StatObjectArgs.builder()
                            .bucket(bucket)
                            .object(objectName)
                            .build()
            );
        } catch (Exception e) {
            log.error("Error consultando statObject en MinIO: {}", e.getMessage());
            throw new MinioException("Error en statObject: " + e.getMessage());
        }
    }

    // -------------------------------------------------------------------------
    //                  (Opcional) Listar objetos en un bucket
    // -------------------------------------------------------------------------

    /**
     * Lista objetos en un bucket, pudiendo filtrar por prefijo.
     */
    public Iterable<Result<Item>> listObjects(String prefix, String bucketName, boolean recursive) {
        String bucket = (bucketName != null) ? bucketName : defaultBucket;
        return getClient().listObjects(
                ListObjectsArgs.builder()
                        .bucket(bucket)
                        .prefix(prefix)
                        .recursive(recursive)
                        .build()
        );
    }

    // -------------------------------------------------------------------------
    //                          Metodo interno: getClient()
    // -------------------------------------------------------------------------
    private MinioClient getClient() {
        return MinioClient.builder()
                .endpoint(minioUrl)
                .credentials(accessKey, secretKey)
                .build();
    }
}
