package corp.jamaro.jamaroservidor.security.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableReactiveMethodSecurity;
import org.springframework.security.config.annotation.rsocket.EnableRSocketSecurity;
import org.springframework.security.config.annotation.rsocket.RSocketSecurity;
import org.springframework.security.rsocket.core.PayloadSocketAcceptorInterceptor;

@Configuration
@EnableRSocketSecurity
@EnableReactiveMethodSecurity
@Slf4j
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationManager jwtAuthenticationManager;

    @Bean
    public PayloadSocketAcceptorInterceptor rsocketInterceptor(RSocketSecurity security) {
        log.info("Configurando seguridad RSocket...");
        return security
                .authorizePayload(authorize -> authorize
                        .setup().permitAll()
                        .route("auth.*").permitAll()
                        .anyRequest().authenticated()
                )
                .jwt(jwtSpec -> {
                    log.info("Configurando autenticación JWT en RSocket...");
                    jwtSpec.authenticationManager(jwtAuthenticationManager);
                })
                .build();
    }
}
