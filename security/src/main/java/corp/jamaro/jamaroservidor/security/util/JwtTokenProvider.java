package corp.jamaro.jamaroservidor.security.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import corp.jamaro.jamaroservidor.security.model.UserAuth;

import java.security.Key;
import java.util.Date;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class JwtTokenProvider {

    @Value("${jwt.secret}")
    private String secretKey;

    @Value("${jwt.expiration}")
    private long validityInSeconds; // Por ejemplo, 86400 = 24h

    private Key key;

    @PostConstruct
    public void init() {
        log.info("Inicializando JwtTokenProvider");
        this.key = Keys.hmacShaKeyFor(secretKey.getBytes());
    }

    /**
     * Crea el token JWT incluyendo los datos del usuario y la metadata del cliente.
     *
     * @param userAuth      La autenticación del usuario.
     * @param clientType    Tipo de cliente (por ejemplo, "desktop", "mobile").
     * @param clientVersion Versión del cliente.
     * @return Token JWT firmado.
     */
    public String createToken(UserAuth userAuth, String clientType, String clientVersion) {
        long now = System.currentTimeMillis();
        Date validity = new Date(now + validityInSeconds * 1000);

        String roles = userAuth.getAuthorities().stream()
                .map(a -> a.getAuthority())
                .collect(Collectors.joining(","));

        log.info("Creando JWT para el usuario: {} con clientType: {} y clientVersion: {}",
                userAuth.getUsername(), clientType, clientVersion);

        return Jwts.builder()
                .setSubject(userAuth.getUsername())
                .claim("roles", roles)
                .claim("userId", userAuth.getUser() != null ? userAuth.getUser().getId() : null)
                .claim("clientType", clientType)
                .claim("clientVersion", clientVersion)
                .setIssuedAt(new Date(now))
                .setExpiration(validity)
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
    }

    public Jws<Claims> validateToken(String token) throws JwtException {
        return Jwts.parserBuilder().setSigningKey(key).build().parseClaimsJws(token);
    }
}
