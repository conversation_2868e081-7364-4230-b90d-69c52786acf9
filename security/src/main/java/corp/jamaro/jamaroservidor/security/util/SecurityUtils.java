package corp.jamaro.jamaroservidor.security.util;

import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.security.enums.RoleEnum;
import corp.jamaro.jamaroservidor.security.model.Role;
import corp.jamaro.jamaroservidor.security.model.UserAuth;
import lombok.experimental.UtilityClass;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.core.context.SecurityContext;
import io.jsonwebtoken.Claims;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@UtilityClass
public class SecurityUtils {

    /**
     * Obtiene el usuario autenticado del Security Context.
     *
     * @return Mono<User> del usuario autenticado.
     */
    public Mono<User> getCurrentUser() {
        return ReactiveSecurityContextHolder.getContext()
                .map(SecurityContext::getAuthentication)
                .filter(Authentication::isAuthenticated)
                .filter(auth -> auth.getPrincipal() instanceof UserAuth)
                .map(auth -> (UserAuth) auth.getPrincipal())
                .map(UserAuth::getUser);
    }

    /**
     * Obtiene el UserAuth completo del usuario autenticado.
     *
     * @return Mono<UserAuth> del usuario autenticado.
     */
    public Mono<UserAuth> getCurrentUserAuth() {
        return ReactiveSecurityContextHolder.getContext()
                .map(SecurityContext::getAuthentication)
                .filter(Authentication::isAuthenticated)
                .filter(auth -> auth.getPrincipal() instanceof UserAuth)
                .map(auth -> (UserAuth) auth.getPrincipal());
    }

    /**
     * Obtiene los roles del usuario autenticado.
     *
     * @return Mono<Set<RoleEnum>> con los roles del usuario.
     */
    public Mono<Set<RoleEnum>> getCurrentUserRoles() {
        return getCurrentUserAuth()
                .map(userAuth -> userAuth.getRoles().stream()
                        .map(Role::getRoleName)
                        .collect(Collectors.toSet()));
    }

    /**
     * Verifica si el usuario actual tiene alguno de los roles especificados.
     *
     * @param roles Roles a verificar
     * @return Mono<Boolean> true si el usuario tiene alguno de los roles
     */
    public Mono<Boolean> hasAnyRole(RoleEnum... roles) {
        Set<RoleEnum> requiredRoles = Set.of(roles);
        return getCurrentUserRoles()
                .map(userRoles -> userRoles.stream().anyMatch(requiredRoles::contains))
                .defaultIfEmpty(false);
    }

    /**
     * Extrae la metadata del cliente (clientType y clientVersion) a partir de los claims
     * presentes en los detalles del Authentication.
     *
     * @return Mono<Map<String, Object>> con la metadata del cliente.
     */
    public Mono<Map<String, Object>> getClientMetadata() {
        return ReactiveSecurityContextHolder.getContext()
                .map(SecurityContext::getAuthentication)
                .filter(Authentication::isAuthenticated)
                .map(auth -> {
                    // Se asume que auth.getDetails() contiene un objeto de tipo Claims
                    if (auth.getDetails() instanceof Claims claims) {
                        return Map.of(
                                "clientType", claims.get("clientType"),
                                "clientVersion", claims.get("clientVersion")
                        );
                    }
                    return Map.of();
                });
    }
}
