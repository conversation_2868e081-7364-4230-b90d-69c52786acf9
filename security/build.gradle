plugins {
    id 'java-library'
    id 'io.spring.dependency-management' version '1.1.6'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.boot:spring-boot-dependencies:3.4.0"
    }
}

group = 'corp.jamaro'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    api 'org.springframework.boot:spring-boot-starter-security' // Changed to api
    implementation 'org.springframework.boot:spring-boot-starter-rsocket' // Added for @MessageMapping
    api 'org.springframework.security:spring-security-rsocket' // Changed to api as it might expose types for @AuthenticationPrincipal etc.
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'

    // JWT with JJWT (already in main, good to specify here if this module is self-contained for security)
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5' // If <PERSON> is used for JWT serialization

    // Dependency on data-neo4j if UserAuth or similar entities are Neo4j nodes/relationships
    // and repositories from data-neo4j are used directly.
    implementation project(':data-neo4j')

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor' // If @ConfigurationProperties are used here

    // Add other dependencies previously in the main build.gradle that are ONLY used by security components
}
