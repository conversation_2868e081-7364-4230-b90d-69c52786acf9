package corp.jamaro.jamaroservidor.app.service;

import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
@RequiredArgsConstructor
public class GeneralService {
    private final UserRepository userRepository;

    @PreAuthorize("hasAnyRole('ADMIN','MANAGER')")
    public Flux<User> getAllUsers() {
        return userRepository.findAll();
    }
}
