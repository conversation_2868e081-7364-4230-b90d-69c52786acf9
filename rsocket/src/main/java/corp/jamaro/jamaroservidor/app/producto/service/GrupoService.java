package corp.jamaro.jamaroservidor.app.producto.service;

import corp.jamaro.jamaroservidor.app.producto.model.Grupo;
import corp.jamaro.jamaroservidor.app.producto.model.NombreGrupo;
import corp.jamaro.jamaroservidor.app.producto.repository.GrupoRepository;
import corp.jamaro.jamaroservidor.app.producto.repository.NombreGrupoRepository;
import corp.jamaro.jamaroservidor.app.util.RegexUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class GrupoService {

    private final GrupoRepository grupoRepository;
    private final NombreGrupoRepository nombreGrupoRepository;

    /**
     * Busca los nodos NombreGrupo cuyo campo 'nombre' coincida con la expresión regular construida a partir del input.
     * Se utiliza RegexUtil para escapar correctamente los caracteres especiales.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite los NombreGrupo que cumplen el criterio.
     */
    public Flux<NombreGrupo> findNombresGrupoByNombre(String userInput) {
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return nombreGrupoRepository.findNombresGrupoByNombre(regex);
    }

    /**
     * Encuentra un Grupo a partir del UUID del NombreGrupo.
     *
     * @param nombreGrupoId UUID del NombreGrupo.
     * @return Un Mono con el Grupo encontrado o vacío si no existe.
     */
    public Mono<Grupo> findGrupoByNombreGrupoId(UUID nombreGrupoId) {
        return grupoRepository.findGrupoByNombreGrupoId(nombreGrupoId);
    }

    /**
     * Encuentra un Grupo por su id.
     *
     * @param id El id del Grupo.
     * @return Un Mono con el Grupo encontrado o vacío si no existe.
     */
    public Mono<Grupo> findGrupoById(String id) {
        return grupoRepository.findById(id);
    }
}
