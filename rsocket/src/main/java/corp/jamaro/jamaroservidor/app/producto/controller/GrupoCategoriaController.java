package corp.jamaro.jamaroservidor.app.producto.controller;

import corp.jamaro.jamaroservidor.app.producto.model.Grupo;
import corp.jamaro.jamaroservidor.app.producto.model.NombreGrupo;
import corp.jamaro.jamaroservidor.app.producto.service.GrupoCategoriaService;
import lombok.RequiredArgsConstructor;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Controller
@MessageMapping("grupo-categoria")
@RequiredArgsConstructor
public class GrupoCategoriaController {

    private final GrupoCategoriaService grupoCategoriaService;

    /**
     * Endpoint para buscar los NombreGrupo cuyo campo nombre coincida con una expresión regular dada
     * y que tengan relación con los nodos Grupo cuyo tipo es categoria.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "grupo-categoria.search.nombres" y un payload que contenga el texto de búsqueda.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Flux con los NombreGrupo que cumplen el criterio.
     */
    @MessageMapping("search.nombres")
    public Flux<NombreGrupo> buscarNombresGrupoPorRegex(String userInput) {
        return grupoCategoriaService.buscarNombresGrupoPorRegex(userInput);
    }

    /**
     * Endpoint para obtener el Grupo asociado a un NombreGrupo a partir del UUID del NombreGrupo.
     * Primero hace una consulta cypher simple y luego un findById para obtener el objeto completo.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "grupo-categoria.get.by-nombreGrupo" y un payload que contenga el UUID del NombreGrupo.
     *
     * @param nombreGrupoId UUID del NombreGrupo.
     * @return Mono con el Grupo encontrado o vacío si no existe.
     */
    @MessageMapping("get.by-nombreGrupo")
    public Mono<Grupo> obtenerGrupoPorNombreGrupoId(UUID nombreGrupoId) {
        return grupoCategoriaService.obtenerGrupoPorNombreGrupoId(nombreGrupoId);
    }

    /**
     * Endpoint para crear un nuevo Grupo del tipo categoria.
     * Recibe un Grupo que el cliente ya construyó con un id asignado.
     * Comprueba que este aún no exista, si no existe y el tipo es categoria procede a hacer un save.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "grupo-categoria.create" y un payload que contenga el Grupo a crear.
     *
     * @param grupo El Grupo a crear.
     * @return Mono con el Grupo creado o error si ya existe o no es del tipo categoria.
     */
    @MessageMapping("create")
    public Mono<Grupo> crearGrupoCategoria(Grupo grupo) {
        return grupoCategoriaService.crearGrupoCategoria(grupo);
    }

    /**
     * Endpoint para actualizar un Grupo existente del tipo categoria.
     * Valida que exista y que sea del tipo categoria, luego usa transaccionalidad para:
     * - Comparar los ids de los nombresGrupo del antiguo con el nuevo y eliminar los que ya no existan
     * - Comparar los ids de los filtros y eliminar las relaciones de los que ya no existan
     * - Los nodos Grupo del tipo categoria no usan el campo subGrupos
     * - Luego hacer el save
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "grupo-categoria.update" y un payload que contenga el Grupo actualizado.
     *
     * @param grupoActualizado El Grupo con los datos actualizados.
     * @return Mono con el Grupo actualizado.
     */
    @MessageMapping("update")
    public Mono<Grupo> actualizarGrupoCategoria(Grupo grupoActualizado) {
        return grupoCategoriaService.actualizarGrupoCategoria(grupoActualizado);
    }
}
