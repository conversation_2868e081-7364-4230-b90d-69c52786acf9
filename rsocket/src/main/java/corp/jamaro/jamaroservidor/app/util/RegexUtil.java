package corp.jamaro.jamaroservidor.app.util;

import corp.jamaro.jamaroservidor.app.producto.model.enums.TipoBusqueda;
import java.util.regex.Pattern;

/**
 * Clase de utilidad para construir expresiones regulares de forma segura.
 * Usamos Pattern.quote para escapar caracteres especiales y garantizamos que la búsqueda sea insensible a mayúsculas.
 */
public final class RegexUtil {

    // Constructor privado para evitar instanciación.
    private RegexUtil() {}

    /**
     * Construye una expresión regular que requiere que la cadena contenga todas las palabras del input.
     * Se ignoran las mayúsculas y se escapan los caracteres especiales.
     *
     * Ejemplo: Para el input "java fx", retorna:
     * (?i)(?=.*java)(?=.*fx).*
     *
     * @param input Texto de entrada.
     * @return Expresión regular segura.
     */
    public static String buildContainsAllRegex(String input) {
        if (input == null || input.trim().isEmpty()) {
            return ".*"; // Coincide con cualquier cadena.
        }
        String[] words = input.trim().split("\\s+");
        StringBuilder regexBuilder = new StringBuilder("(?i)"); // Hacemos la búsqueda insensible a mayúsculas.
        for (String word : words) {
            regexBuilder.append("(?=.*").append(Pattern.quote(word)).append(")");
        }
        regexBuilder.append(".*");
        return regexBuilder.toString();
    }

    /**
     * Construye una expresión regular que verifica si una cadena inicia con el input dado.
     * Se ignoran las mayúsculas y se escapan los caracteres especiales.
     *
     * Ejemplo: Para el input "Spring", retorna:
     * (?i)^Spring.*
     *
     * @param input Texto de entrada.
     * @return Expresión regular que valida el inicio de la cadena.
     */
    public static String buildStartsWithRegex(String input) {
        if (input == null || input.trim().isEmpty()) {
            return ".*";
        }
        return "(?i)^" + Pattern.quote(input.trim()) + ".*";
    }

    /**
     * Construye una expresión regular para una búsqueda exacta.
     * Se ignoran las mayúsculas y se escapan los caracteres especiales.
     *
     * Ejemplo: Para el input "Spring", retorna:
     * (?i)^Spring$
     *
     * @param input Texto de entrada.
     * @return Expresión regular que valida una coincidencia exacta.
     */
    public static String buildExactMatchRegex(String input) {
        if (input == null || input.trim().isEmpty()) {
            return ".*";
        }
        return "(?i)^" + Pattern.quote(input.trim()) + "$";
    }

    /**
     * Construye una expresión regular para una búsqueda que excluye el valor dado.
     * Se ignoran las mayúsculas y se escapan los caracteres especiales.
     *
     * @param input Texto de entrada a excluir.
     * @return Expresión regular que valida que no coincida exactamente con el input.
     */
    public static String buildNotEqualsRegex(String input) {
        if (input == null || input.trim().isEmpty()) {
            return ".*";
        }
        // Usamos negative lookahead para excluir la coincidencia exacta
        return "(?i)(?!^" + Pattern.quote(input.trim()) + "$).*";
    }

    /**
     * Genera la expresión regular adecuada según el tipo de búsqueda especificado.
     *
     * @param input Texto de entrada para la búsqueda.
     * @param tipoBusqueda Tipo de búsqueda a realizar (CONTIENE, EXACTA, EMPIEZA, etc.)
     * @return Expresión regular correspondiente al tipo de búsqueda.
     */
    public static String buildRegexByTipoBusqueda(String input, TipoBusqueda tipoBusqueda) {
        if (input == null || input.trim().isEmpty()) {
            return ".*";
        }

        // Si tipoBusqueda es null o CONTIENE (caso por defecto), usamos buildContainsAllRegex
        if (tipoBusqueda == null || tipoBusqueda == TipoBusqueda.CONTIENE) {
            return buildContainsAllRegex(input);
        }

        // Seleccionamos el tipo de regex según el tipo de búsqueda
        switch (tipoBusqueda) {
            case EXACTA:
                return buildExactMatchRegex(input);
            case EMPIEZA:
                return buildStartsWithRegex(input);
            case DIFERENTE_DE:
                return buildNotEqualsRegex(input);
            default:
                // Si es un tipo no manejado explícitamente, usamos el comportamiento por defecto
                return buildContainsAllRegex(input);
        }
        // Nota: Los casos MAYOR_QUE y MENOR_QUE no se manejan con regex sino con comparaciones numéricas
    }
}
