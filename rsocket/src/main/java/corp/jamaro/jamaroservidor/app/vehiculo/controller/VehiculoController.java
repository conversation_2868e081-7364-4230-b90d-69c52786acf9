package corp.jamaro.jamaroservidor.app.vehiculo.controller;

import corp.jamaro.jamaroservidor.app.vehiculo.model.Vehiculo;
import corp.jamaro.jamaroservidor.app.vehiculo.model.VehiculoMarca;
import corp.jamaro.jamaroservidor.app.vehiculo.model.VehiculoModelo;
import corp.jamaro.jamaroservidor.app.vehiculo.model.VehiculoMotor;
import corp.jamaro.jamaroservidor.app.vehiculo.model.VehiculoNombre;
import corp.jamaro.jamaroservidor.app.vehiculo.service.VehiculoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Slf4j
@Controller
@MessageMapping("vehiculo")
@RequiredArgsConstructor
public class VehiculoController {

    private final VehiculoService vehiculoService;

    /**
     * Endpoint para buscar los VehiculoNombre cuyo campo nombre coincida con una expresión regular dada.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.search.nombres" y un payload que contenga el texto de búsqueda.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Flux con los VehiculoNombre que cumplen el criterio.
     */
    @MessageMapping("search.nombres")
    public Flux<VehiculoNombre> buscarNombresVehiculoPorRegex(String userInput) {
        return vehiculoService.buscarNombresVehiculoPorRegex(userInput);
    }

    /**
     * Endpoint para obtener el Vehiculo asociado a un VehiculoNombre a partir del UUID del VehiculoNombre.
     * Primero hace una consulta cypher simple y luego un findById para obtener el objeto completo.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.get.by-nombreVehiculo" y un payload que contenga el UUID del VehiculoNombre.
     *
     * @param nombreVehiculoId UUID del VehiculoNombre.
     * @return Mono con el Vehiculo encontrado o vacío si no existe.
     */
    @MessageMapping("get.by-nombreVehiculo")
    public Mono<Vehiculo> obtenerVehiculoPorNombreVehiculoId(UUID nombreVehiculoId) {
        return vehiculoService.obtenerVehiculoPorNombreVehiculoId(nombreVehiculoId);
    }

    /**
     * Endpoint para crear un nuevo Vehiculo.
     * Recibe un Vehiculo que el cliente ya construyó con un id asignado.
     * Comprueba que este aún no exista, si no existe procede a hacer un save.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.create" y un payload que contenga el Vehiculo a crear.
     *
     * @param vehiculo El Vehiculo a crear.
     * @return Mono con el Vehiculo creado o error si ya existe.
     */
    @MessageMapping("create")
    public Mono<Vehiculo> crearVehiculo(Vehiculo vehiculo) {
        return vehiculoService.crearVehiculo(vehiculo);
    }

    /**
     * Endpoint para actualizar un Vehiculo existente.
     * Valida que exista, luego usa transaccionalidad para:
     * - Comparar los ids de los nombres del antiguo con el nuevo y eliminar los que ya no existan
     * - Comparar los ids de los años y eliminar los que ya no existan
     * - Comparar los ids de los archivos y eliminar las relaciones de los que ya no existan
     * - Luego hacer el save
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.update" y un payload que contenga el Vehiculo actualizado.
     *
     * @param vehiculoActualizado El Vehiculo con los datos actualizados.
     * @return Mono con el Vehiculo actualizado.
     */
    @MessageMapping("update")
    public Mono<Vehiculo> actualizarVehiculo(Vehiculo vehiculoActualizado) {
        return vehiculoService.actualizarVehiculo(vehiculoActualizado);
    }

    /**
     * Endpoint para obtener todas las VehiculoMarca disponibles.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.get.all-marcas" sin payload o con payload null.
     *
     * @return Flux con todas las VehiculoMarca.
     */
    @MessageMapping("get.all-marcas")
    public Flux<VehiculoMarca> obtenerTodasLasMarcas() {
        return vehiculoService.obtenerTodasLasMarcas();
    }

    /**
     * Endpoint para obtener todos los VehiculoModelo disponibles.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.get.all-modelos" sin payload o con payload null.
     *
     * @return Flux con todos los VehiculoModelo.
     */
    @MessageMapping("get.all-modelos")
    public Flux<VehiculoModelo> obtenerTodosLosModelos() {
        return vehiculoService.obtenerTodosLosModelos();
    }

    /**
     * Endpoint para obtener todos los VehiculoMotor disponibles.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.get.all-motores" sin payload o con payload null.
     *
     * @return Flux con todos los VehiculoMotor.
     */
    @MessageMapping("get.all-motores")
    public Flux<VehiculoMotor> obtenerTodosLosMotores() {
        return vehiculoService.obtenerTodosLosMotores();
    }

    /**
     * Endpoint para obtener sugerencias de cilindrada basadas en el input del usuario.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.get.cilindrada-suggestions" y un payload que contenga el texto de búsqueda.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Flux con sugerencias de cilindrada.
     */
    @MessageMapping("get.cilindrada-suggestions")
    public Flux<String> obtenerSugerenciasCilindrada(String userInput) {
        return vehiculoService.obtenerSugerenciasCilindrada(userInput);
    }

    /**
     * Endpoint para obtener sugerencias de versión basadas en el input del usuario.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.get.version-suggestions" y un payload que contenga el texto de búsqueda.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Flux con sugerencias de versión.
     */
    @MessageMapping("get.version-suggestions")
    public Flux<String> obtenerSugerenciasVersion(String userInput) {
        return vehiculoService.obtenerSugerenciasVersion(userInput);
    }

    /**
     * Endpoint para obtener sugerencias de carrocería basadas en el input del usuario.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.get.carroceria-suggestions" y un payload que contenga el texto de búsqueda.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Flux con sugerencias de carrocería.
     */
    @MessageMapping("get.carroceria-suggestions")
    public Flux<String> obtenerSugerenciasCarroceria(String userInput) {
        return vehiculoService.obtenerSugerenciasCarroceria(userInput);
    }

    /**
     * Endpoint para obtener sugerencias de tipo de tracción basadas en el input del usuario.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.get.tipo-traccion-suggestions" y un payload que contenga el texto de búsqueda.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Flux con sugerencias de tipo de tracción.
     */
    @MessageMapping("get.tipo-traccion-suggestions")
    public Flux<String> obtenerSugerenciasTipoTraccion(String userInput) {
        return vehiculoService.obtenerSugerenciasTipoTraccion(userInput);
    }

    /**
     * Endpoint para obtener sugerencias de transmisión basadas en el input del usuario.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "vehiculo.get.transmision-suggestions" y un payload que contenga el texto de búsqueda.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Flux con sugerencias de transmisión.
     */
    @MessageMapping("get.transmision-suggestions")
    public Flux<String> obtenerSugerenciasTransmision(String userInput) {
        return vehiculoService.obtenerSugerenciasTransmision(userInput);
    }
}
