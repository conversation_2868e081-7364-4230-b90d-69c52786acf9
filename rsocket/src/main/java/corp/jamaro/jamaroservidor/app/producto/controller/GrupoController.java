package corp.jamaro.jamaroservidor.app.producto.controller;

import corp.jamaro.jamaroservidor.app.producto.model.Grupo;
import corp.jamaro.jamaroservidor.app.producto.model.NombreGrupo;
import corp.jamaro.jamaroservidor.app.producto.service.GrupoService;
import lombok.RequiredArgsConstructor;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Controller
@MessageMapping("grupo")
@RequiredArgsConstructor
public class GrupoController {

    private final GrupoService grupoService;

    /**
     * Endpoint para buscar nodos NombreGrupo cuyo campo 'nombre' coincida con la expresión regular indicada.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "grupo.search.nombres" y un payload que contenga la expresión regular.
     *
     * @param regex Expresión regular para filtrar el nombre.
     * @return Flux con los nodos NombreGrupo que cumplen el criterio.
     */
    @MessageMapping("search.nombres")
    public Flux<NombreGrupo> searchNombresGrupo(String regex) {
        return grupoService.findNombresGrupoByNombre(regex);
    }

    /**
     * Endpoint para obtener el Grupo asociado a un NombreGrupo a partir del UUID del NombreGrupo.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "grupo.get.by-nombreGrupo" y un payload que contenga el UUID del NombreGrupo.
     *
     * @param nombreGrupoId UUID del NombreGrupo.
     * @return Mono con el Grupo encontrado o vacío si no existe.
     */
    @MessageMapping("get.by-nombreGrupo")
    public Mono<Grupo> getGrupoByNombreGrupoId(UUID nombreGrupoId) {
        return grupoService.findGrupoByNombreGrupoId(nombreGrupoId);
    }

    /**
     * Endpoint para obtener un Grupo a partir de su id.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "grupo.get.by-id" y un payload que contenga el id del Grupo.
     *
     * @param id El id del Grupo.
     * @return Mono con el Grupo encontrado o vacío si no existe.
     */
    @MessageMapping("get.by-id")
    public Mono<Grupo> getGrupoById(String id) {
        return grupoService.findGrupoById(id);
    }
}
