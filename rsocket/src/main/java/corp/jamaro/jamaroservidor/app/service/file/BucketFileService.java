package corp.jamaro.jamaroservidor.app.service.file;

import corp.jamaro.jamaroservidor.app.model.file.BucketFile;
import corp.jamaro.jamaroservidor.app.repository.file.BucketFileRepository;
import io.minio.errors.MinioException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.UUID;

/**
 * Servicio para CRUD de BucketFile en Neo4j (reactivo).
 * Integra opcionalmente la interacción con MinIO (por ejemplo, para eliminar objetos).
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BucketFileService {

    private final BucketFileRepository bucketFileRepository;
    private final MinioService minioService;

    /**
     * Crea o actualiza un BucketFile en Neo4j.
     * (En Spring Data Neo4j reactivo, .save() sirve para crear o actualizar).
     *
     * @param bucketFile Entidad con los datos a guardar.
     * @return Mono<BucketFile> con la entidad guardada en BD.
     */
    @Transactional
    public Mono<BucketFile> save(BucketFile bucketFile) {
        // Actualizamos la marca de tiempo
        bucketFile.setCreadoActualizado(Instant.now());

        return bucketFileRepository.save(bucketFile)
                .onErrorMap(OptimisticLockingFailureException.class, ex -> {
                    // Manejo ejemplo de locking optimista si usas @Version (opcionales en tu modelo)
                    log.error("Conflicto de versión al guardar BucketFile: {}", ex.getMessage());
                    return new RuntimeException("Otro usuario modificó este BucketFile simultáneamente.", ex);
                });
    }

    /**
     * Obtiene un BucketFile por su ID (UUID).
     */
    public Mono<BucketFile> findById(UUID id) {
        return bucketFileRepository.findById(id);
    }

    /**
     * Obtiene todos los BucketFile guardados en BD.
     */
    public Flux<BucketFile> findAll() {
        return bucketFileRepository.findAll();
    }

    /**
     * Elimina un BucketFile por ID tanto en Neo4j como en MinIO (opcional).
     * Si quieres que NO se borre del bucket de MinIO, elimina esa lógica.
     *
     * @param id ID del BucketFile.
     * @return Mono<Void> que completa cuando all se ha eliminado.
     */
    @Transactional
    public Mono<Void> deleteById(UUID id) {
        return bucketFileRepository.findById(id)
                .flatMap(bucketFile -> {
                    // 1) Intentar eliminar del bucket de MinIO
                    if (bucketFile.getObjectName() != null) {
                        try {
                            minioService.removeObject(bucketFile.getObjectName(), null);
                            log.debug("Objeto en MinIO eliminado con objectName={}", bucketFile.getObjectName());
                        } catch (MinioException e) {
                            // Decide cómo manejar el error. Podrías:
                            // - Loguear y continuar
                            // - Retornar un error para abortar la operación
                            log.error("Error al eliminar objeto en MinIO: {}", e.getMessage());
                        }
                    }

                    // 2) Eliminar el registro de la base de datos
                    return bucketFileRepository.delete(bucketFile);
                });
    }

    /**
     * Ejemplo de metodo para generar una URL prefirmada de subida (PUT).
     * Luego se puede guardar el BucketFile con los datos (objectName, etc.)
     */
    public Mono<String> getPresignedUrlForUpload(UUID bucketFileId, long expireSeconds) {
        return bucketFileRepository.findById(bucketFileId)
                .flatMap(bf -> {
                    // Usamos el objectName que ya tiene.
                    // Si en tu flujo es un BucketFile nuevo, podrías crear primero el registro.
                    String objectName = bf.getObjectName();
                    return Mono.fromCallable(() -> {
                        // Generar la URL prefirmada de subida
                        try {
                            return minioService.getPresignedUrlForUpload(objectName, expireSeconds, null);
                        } catch (MinioException e) {
                            throw new RuntimeException("Error al generar URL prefirmada (PUT): " + e.getMessage(), e);
                        }
                    });
                });
    }

    /**
     * Ejemplo de metodo para actualizar la propiedad 'etag' de un BucketFile.
     * Podrías llamarlo después de que el cliente haya subido el archivo a MinIO y obtenga el ETag.
     */
    public Mono<BucketFile> updateEtag(UUID bucketFileId, String etag) {
        return bucketFileRepository.findById(bucketFileId)
                .switchIfEmpty(Mono.error(new RuntimeException("BucketFile no encontrado con id=" + bucketFileId)))
                .flatMap(bf -> {
                    bf.setEtag(etag);
                    bf.setCreadoActualizado(Instant.now());
                    return bucketFileRepository.save(bf);
                });
    }

}
