package corp.jamaro.jamaroservidor.app.producto.controller;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.producto.service.ItemMantenimientoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

/**
 * Controlador para el mantenimiento de Items.
 * Proporciona endpoints RSocket para operaciones CRUD de Items.
 */
@Controller
@MessageMapping("item-mantenimiento")
@RequiredArgsConstructor
@Slf4j
public class ItemMantenimientoController {

    private final ItemMantenimientoService itemMantenimientoService;

    /**
     * Endpoint para buscar un Item por su codCompuesto.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "item-mantenimiento.search.by-cod-compuesto" y un payload que contenga el código compuesto del Item.
     *
     * @param codCompuesto Código compuesto del Item.
     * @return Mono con el Item encontrado o vacío si no existe.
     */
    @MessageMapping("search.by-cod-compuesto")
    public Mono<Item> buscarItemPorCodCompuesto(String codCompuesto) {
        return itemMantenimientoService.buscarItemPorCodCompuesto(codCompuesto);
    }

    /**
     * Endpoint para verificar si existe un Item con el codCompuesto especificado.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "item-mantenimiento.exists.by-cod-compuesto" y un payload que contenga el código compuesto del Item.
     *
     * @param codCompuesto Código compuesto del Item a verificar.
     * @return Mono con true si existe, false en caso contrario.
     */
    @MessageMapping("exists.by-cod-compuesto")
    public Mono<Boolean> existeItemPorCodCompuesto(String codCompuesto) {
        return itemMantenimientoService.existeItemPorCodCompuesto(codCompuesto);
    }

    /**
     * Endpoint para crear un nuevo Item.
     * Genera automáticamente el codCompuesto usando la lógica establecida
     * (Producto.codProductoOld + Marca.abreviacion). Procesa los atributos del item para generar sus IDs.
     * Valida que el codCompuesto generado no exista previamente.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "item-mantenimiento.create" y un payload que contenga el Item a crear.
     *
     * @param item El Item a crear.
     * @return Mono con el Item creado o error si ya existe.
     */
    @MessageMapping("create")
    public Mono<Item> crearItem(Item item) {
        return itemMantenimientoService.crearItem(item);
    }

    /**
     * Endpoint para actualizar un Item existente.
     * Valida que exista el Item con el codCompuesto especificado.
     * Procesa los atributos del item para generar sus IDs según la lógica establecida.
     * Usa transaccionalidad para garantizar la consistencia de los datos.
     * 
     * IMPORTANTE: El codCompuesto NO se puede cambiar en una actualización, ya que es el ID del Item.
     * Si se necesita cambiar el Producto o la Marca, se debe eliminar el Item y crear uno nuevo.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "item-mantenimiento.update" y un payload que contenga el Item actualizado.
     *
     * @param itemActualizado El Item con los datos actualizados.
     * @return Mono con el Item actualizado.
     */
    @MessageMapping("update")
    public Mono<Item> actualizarItem(Item itemActualizado) {
        return itemMantenimientoService.actualizarItem(itemActualizado);
    }
}