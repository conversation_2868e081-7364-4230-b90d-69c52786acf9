package corp.jamaro.jamaroservidor.app.vehiculo.service;

import corp.jamaro.jamaroservidor.app.util.RegexUtil;
import corp.jamaro.jamaroservidor.app.vehiculo.model.Vehiculo;
import corp.jamaro.jamaroservidor.app.vehiculo.model.VehiculoMarca;
import corp.jamaro.jamaroservidor.app.vehiculo.model.VehiculoModelo;
import corp.jamaro.jamaroservidor.app.vehiculo.model.VehiculoMotor;
import corp.jamaro.jamaroservidor.app.vehiculo.model.VehiculoNombre;
import corp.jamaro.jamaroservidor.app.vehiculo.repository.VehiculoMarcaRepository;
import corp.jamaro.jamaroservidor.app.vehiculo.repository.VehiculoModeloRepository;
import corp.jamaro.jamaroservidor.app.vehiculo.repository.VehiculoMotorRepository;
import corp.jamaro.jamaroservidor.app.vehiculo.repository.VehiculoNombreRepository;
import corp.jamaro.jamaroservidor.app.vehiculo.repository.VehiculoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class VehiculoService {

    private final TransactionalOperator transactionalOperator;
    private final VehiculoRepository vehiculoRepository;
    private final VehiculoNombreRepository vehiculoNombreRepository;
    private final VehiculoMarcaRepository vehiculoMarcaRepository;
    private final VehiculoModeloRepository vehiculoModeloRepository;
    private final VehiculoMotorRepository vehiculoMotorRepository;

    /**
     * Busca los VehiculoNombre cuyo campo nombre coincida con una expresión regular dada.
     * Se utiliza RegexUtil para escapar correctamente los caracteres especiales.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Un Flux que emite los VehiculoNombre que cumplen el criterio.
     */
    public Flux<VehiculoNombre> buscarNombresVehiculoPorRegex(String userInput) {
        log.info("Buscando VehiculoNombre por regex: {}", userInput);
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return vehiculoNombreRepository.findByNombreRegex(regex)
                .doOnNext(vehiculoNombre -> log.info("VehiculoNombre encontrado y devuelto: {}", vehiculoNombre.getNombre()));
    }

    /**
     * Recibe el id de un VehiculoNombre y obtiene el Vehiculo con el que tiene relación.
     * Primero hace una consulta cypher simple en el repository y luego un findById para obtener el objeto completo.
     *
     * @param nombreVehiculoId UUID del VehiculoNombre.
     * @return Un Mono con el Vehiculo encontrado o vacío si no existe.
     */
    public Mono<Vehiculo> obtenerVehiculoPorNombreVehiculoId(UUID nombreVehiculoId) {
        log.info("Obteniendo Vehiculo por VehiculoNombre id: {}", nombreVehiculoId);

        return vehiculoRepository.findVehiculoByNombreVehiculoId(nombreVehiculoId)
                .flatMap(vehiculo -> vehiculoRepository.findById(vehiculo.getId()));
    }

    /**
     * Crea un nuevo Vehiculo. Recibe un Vehiculo que el cliente ya construyó 
     * con un id asignado. Comprueba que este aún no exista, si no existe 
     * procede a hacer un save.
     *
     * @param vehiculo El Vehiculo a crear.
     * @return Un Mono con el Vehiculo creado o error si ya existe.
     */
    public Mono<Vehiculo> crearVehiculo(Vehiculo vehiculo) {
        log.info("Creando nuevo Vehiculo con id: {}", vehiculo.getId());

        // Verificar que no exista
        return vehiculoRepository.existsById(vehiculo.getId())
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new IllegalArgumentException("Ya existe un Vehiculo con el id: " + vehiculo.getId()));
                    }
                    return vehiculoRepository.save(vehiculo);
                })
                .flatMap(vehiculoCreado -> limpiarNodosHuerfanos().thenReturn(vehiculoCreado));
    }

    /**
     * Actualiza un Vehiculo existente. Valida que exista.
     * Usa transaccionalidad y varios métodos internos para actualizar de manera eficiente:
     * - Compara los ids de los nombres del antiguo con el nuevo y elimina los que ya no existan
     * - Compara los ids de los años y elimina los que ya no existan
     * - Compara los ids de los archivos y elimina las relaciones de los que ya no existan
     * - Luego hace el save
     *
     * @param vehiculoActualizado El Vehiculo con los datos actualizados.
     * @return Un Mono con el Vehiculo actualizado.
     */
    public Mono<Vehiculo> actualizarVehiculo(Vehiculo vehiculoActualizado) {
        log.info("Actualizando Vehiculo con id: {}", vehiculoActualizado.getId());

        return transactionalOperator.transactional(
                vehiculoRepository.findById(vehiculoActualizado.getId())
                        .switchIfEmpty(Mono.error(new IllegalArgumentException("No existe un Vehiculo con el id: " + vehiculoActualizado.getId())))
                        .flatMap(vehiculoExistente -> {
                            return limpiarRelacionesObsoletas(vehiculoExistente, vehiculoActualizado)
                                    .then(vehiculoRepository.save(vehiculoActualizado))
                                    .flatMap(vehiculoGuardado -> limpiarNodosHuerfanos().thenReturn(vehiculoGuardado));
                        })
        );
    }

    /**
     * Obtiene todas las VehiculoMarca disponibles.
     *
     * @return Un Flux que emite todas las VehiculoMarca.
     */
    public Flux<VehiculoMarca> obtenerTodasLasMarcas() {
        log.info("Obteniendo todas las VehiculoMarca");
        return vehiculoMarcaRepository.findAll()
                .doOnNext(marca -> log.debug("VehiculoMarca encontrada: {}", marca.getMarca()));
    }

    /**
     * Obtiene todos los VehiculoModelo disponibles.
     *
     * @return Un Flux que emite todos los VehiculoModelo.
     */
    public Flux<VehiculoModelo> obtenerTodosLosModelos() {
        log.info("Obteniendo todos los VehiculoModelo");
        return vehiculoModeloRepository.findAll()
                .doOnNext(modelo -> log.debug("VehiculoModelo encontrado: {}", modelo.getModelo()));
    }

    /**
     * Obtiene todos los VehiculoMotor disponibles.
     *
     * @return Un Flux que emite todos los VehiculoMotor.
     */
    public Flux<VehiculoMotor> obtenerTodosLosMotores() {
        log.info("Obteniendo todos los VehiculoMotor");
        return vehiculoMotorRepository.findAll()
                .doOnNext(motor -> log.debug("VehiculoMotor encontrado: {}", motor.getMotor()));
    }

    /**
     * Obtiene sugerencias de cilindrada basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de cilindrada.
     */
    public Flux<String> obtenerSugerenciasCilindrada(String userInput) {
        log.info("Obteniendo sugerencias de cilindrada para: {}", userInput);
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return vehiculoRepository.findDistinctCilindradaByRegex(regex)
                .doOnNext(cilindrada -> log.debug("Sugerencia de cilindrada encontrada: {}", cilindrada));
    }

    /**
     * Obtiene sugerencias de versión basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de versión.
     */
    public Flux<String> obtenerSugerenciasVersion(String userInput) {
        log.info("Obteniendo sugerencias de versión para: {}", userInput);
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return vehiculoRepository.findDistinctVersionByRegex(regex)
                .doOnNext(version -> log.debug("Sugerencia de versión encontrada: {}", version));
    }

    /**
     * Obtiene sugerencias de carrocería basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de carrocería.
     */
    public Flux<String> obtenerSugerenciasCarroceria(String userInput) {
        log.info("Obteniendo sugerencias de carrocería para: {}", userInput);
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return vehiculoRepository.findDistinctCarroceriaByRegex(regex)
                .doOnNext(carroceria -> log.debug("Sugerencia de carrocería encontrada: {}", carroceria));
    }

    /**
     * Obtiene sugerencias de tipo de tracción basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de tipo de tracción.
     */
    public Flux<String> obtenerSugerenciasTipoTraccion(String userInput) {
        log.info("Obteniendo sugerencias de tipo de tracción para: {}", userInput);
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return vehiculoRepository.findDistinctTipoTraccionByRegex(regex)
                .doOnNext(tipoTraccion -> log.debug("Sugerencia de tipo de tracción encontrada: {}", tipoTraccion));
    }

    /**
     * Obtiene sugerencias de transmisión basadas en el input del usuario.
     *
     * @param userInput Texto ingresado por el usuario.
     * @return Un Flux que emite sugerencias de transmisión.
     */
    public Flux<String> obtenerSugerenciasTransmision(String userInput) {
        log.info("Obteniendo sugerencias de transmisión para: {}", userInput);
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return vehiculoRepository.findDistinctTransmisionByRegex(regex)
                .doOnNext(transmision -> log.debug("Sugerencia de transmisión encontrada: {}", transmision));
    }

    /**
     * Método interno para limpiar las relaciones obsoletas antes de actualizar.
     * Por ahora es un placeholder - se implementará según las necesidades específicas del modelo Vehiculo.
     */
    private Mono<Void> limpiarRelacionesObsoletas(Vehiculo vehiculoExistente, Vehiculo vehiculoActualizado) {
        // TODO: Implementar limpieza de relaciones obsoletas similar a GrupoCategoriaService
        // - Limpiar nombres obsoletos
        // - Limpiar años obsoletos  
        // - Limpiar archivos obsoletos
        return Mono.empty();
    }

    /**
     * Método interno que elimina los nodos huérfanos después de las operaciones de creación o actualización.
     * Por ahora es un placeholder - se implementará según las necesidades específicas del modelo Vehiculo.
     */
    private Mono<Void> limpiarNodosHuerfanos() {
        // TODO: Implementar limpieza de nodos huérfanos similar a GrupoCategoriaService
        // - Eliminar VehiculoNombre huérfanos
        // - Eliminar VehiculoAnio huérfanos
        log.info("Limpieza de nodos huérfanos completada (placeholder)");
        return Mono.empty();
    }
}
