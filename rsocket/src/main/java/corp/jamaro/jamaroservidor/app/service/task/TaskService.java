package corp.jamaro.jamaroservidor.app.service.task;

import corp.jamaro.jamaroservidor.app.model.task.Task;
import corp.jamaro.jamaroservidor.app.repository.task.TaskRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;


@RequiredArgsConstructor
@Service
public class TaskService {

    private final TaskRepository taskRepository;


    public Mono<Task> createOrUpdate(Task task) {
        // Crea o actualiza una tarea genérica o una subclase
        return taskRepository.save(task);
    }

    public Mono<Task> getById(UUID id) {
        return taskRepository.findById(id);
    }

    public Flux<Task> getAll() {
        return taskRepository.findAll();
    }

    public Mono<Void> deleteById(UUID id) {
        return taskRepository.deleteById(id);
    }

    // Métodos específicos, e.g. buscar por estado, prioridad, etc.
    // public Flux<Task> findByEstado(TaskStatusEnum estado) { ... }
}
