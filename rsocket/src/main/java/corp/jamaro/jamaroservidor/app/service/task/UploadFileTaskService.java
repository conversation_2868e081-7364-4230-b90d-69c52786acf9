package corp.jamaro.jamaroservidor.app.service.task;

import corp.jamaro.jamaroservidor.app.model.enums.TaskStatusEnum;
import corp.jamaro.jamaroservidor.app.model.file.BucketFile;
import corp.jamaro.jamaroservidor.app.model.task.Task;
import corp.jamaro.jamaroservidor.app.model.task.UploadFileTask;
import corp.jamaro.jamaroservidor.app.repository.task.UploadFileTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@RequiredArgsConstructor
@Service
@Slf4j
public class UploadFileTaskService {

    private final UploadFileTaskRepository uploadFileTaskRepository;


    // Métodos específicos para UploadFileTask
    public Mono<UploadFileTask> createOrUpdate(UploadFileTask task) {
        return uploadFileTaskRepository.save(task);
    }
    public Mono<UploadFileTask> getById(UUID id) {
        return uploadFileTaskRepository.findById(id);
    }

    // Lista todas las UploadFileTask
    public Flux<UploadFileTask> getAll() {
        return uploadFileTaskRepository.findAll();
    }

    public Mono<Void> deleteById(UUID id) {
        return uploadFileTaskRepository.deleteById(id);
    }

    // Marca la tarea como completada, estableciendo el progreso a 100%
    public Mono<UploadFileTask> markAsCompleted(UUID id) {
        return uploadFileTaskRepository.findById(id)
                .flatMap(task -> {
                    log.info("Marcando como completada la UploadFileTask con id={}", id);
                    task.setEstado(TaskStatusEnum.COMPLETADA);
                    task.setProgress(100f);
                    return uploadFileTaskRepository.save(task);
                });
    }

    // Actualiza el progreso de la tarea y cambia el estado a EN_PROGRESO si no está completada
    public Mono<UploadFileTask> setProgress(UUID id, float newProgress) {
        return uploadFileTaskRepository.findById(id)
                .flatMap(task -> {
                    log.debug("Actualizando progreso a {} para UploadFileTask con id={}", newProgress, id);
                    task.setProgress(newProgress);
                    // Cambia estado a EN_PROGRESO si no es COMPLETADA
                    if (newProgress < 100f) {
                        task.setEstado(TaskStatusEnum.EN_PROGRESO);
                    }
                    return uploadFileTaskRepository.save(task);
                });
    }

    // Actualiza la información del BucketFile
    public Mono<UploadFileTask> updateBucketFile(UUID id, BucketFile newBucketFile) {
        return uploadFileTaskRepository.findById(id)
                .flatMap(task -> {
                    log.info("Actualizando BucketFile de UploadFileTask con id={}", id);
                    task.setBucketFile(newBucketFile);
                    return uploadFileTaskRepository.save(task);
                });
    }

}
