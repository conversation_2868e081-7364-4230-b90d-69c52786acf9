package corp.jamaro.jamaroservidor.app.caja.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;

/**
 * Controlador para gestionar las operaciones relacionadas con cobros de caja a través de RSocket.
 * Proporciona endpoints para realizar cobros en efectivo y/o digital.
 */
@Controller
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER', 'ADMINISTRATOR', 'CASHIER')")
public class CajaCobrosController {

}
