package corp.jamaro.jamaroservidor.app.controller;

import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.service.GeneralService;
import lombok.RequiredArgsConstructor;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;

@Controller
@RequiredArgsConstructor
public class GeneralController {
    private final GeneralService generalService;

    @MessageMapping("users.getAll")
    public Flux<User> getAllUsers() {
        return generalService.getAllUsers();
    }
}
