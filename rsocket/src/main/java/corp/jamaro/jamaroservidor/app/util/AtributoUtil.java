package corp.jamaro.jamaroservidor.app.util;

import corp.jamaro.jamaroservidor.app.producto.model.Atributo;
import corp.jamaro.jamaroservidor.app.producto.repository.AtributoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Base64;
import java.util.Iterator;
import java.util.Set;

/**
 * Utilidad para el manejo de Atributos.
 * Proporciona métodos reutilizables para la generación de IDs y procesamiento de Atributos.
 */
@Slf4j
@Component
public class AtributoUtil {

    private final AtributoRepository atributoRepository;

    @Autowired
    public AtributoUtil(AtributoRepository atributoRepository) {
        this.atributoRepository = atributoRepository;
    }

    /**
     * Genera el ID de un Atributo siguiendo la lógica: filtro.id + ":" + base64(dato)
     * Asume que el dato ya ha sido convertido a minúsculas previamente.
     * 
     * @param atributo El Atributo para el cual generar el ID
     * @return El ID generado para el Atributo
     * @throws IllegalArgumentException si el Atributo, su Filtro o dato son nulos
     */
    public String generarAtributoId(Atributo atributo) {
        if (atributo == null) {
            throw new IllegalArgumentException("El Atributo no puede ser nulo");
        }
        if (atributo.getFiltro() == null || atributo.getFiltro().getId() == null) {
            throw new IllegalArgumentException("El Atributo debe tener un Filtro con ID válido");
        }
        if (atributo.getDato() == null) {
            throw new IllegalArgumentException("El Atributo debe tener un dato válido");
        }
        
        String filtroId = atributo.getFiltro().getId().toString();
        // El dato ya debe estar en minúsculas cuando llega aquí
        String datoBase64 = Base64.getEncoder().encodeToString(atributo.getDato().getBytes());
        String generatedId = filtroId + ":" + datoBase64;
        
        log.debug("Atributo ID generado: {} para filtro: {} y dato: {}", 
                 generatedId, filtroId, atributo.getDato());
        
        return generatedId;
    }

    /**
     * Filtra y elimina atributos con datos nulos o vacíos del conjunto.
     * Esto previene errores durante el procesamiento y la limpieza de atributos huérfanos.
     * 
     * @param atributos El conjunto de atributos a filtrar
     * @return El número de atributos eliminados
     */
    private int filtrarAtributosInvalidos(Set<Atributo> atributos) {
        if (atributos == null || atributos.isEmpty()) {
            return 0;
        }
        
        int atributosEliminados = 0;
        Iterator<Atributo> iterator = atributos.iterator();
        
        while (iterator.hasNext()) {
            Atributo atributo = iterator.next();
            
            // Verificar si el atributo tiene datos nulos o vacíos (incluyendo espacios en blanco)
            if (atributo.getDato() == null || atributo.getDato().trim().isEmpty()) {
                log.info("Eliminando atributo con dato nulo o vacío. Filtro: {}, Dato: '{}'", 
                        atributo.getFiltro() != null ? atributo.getFiltro().getId() : "null", 
                        atributo.getDato());
                iterator.remove();
                atributosEliminados++;
            }
        }
        
        if (atributosEliminados > 0) {
            log.info("Se eliminaron {} atributos con datos nulos o vacíos del producto", atributosEliminados);
        }
        
        return atributosEliminados;
    }

    /**
     * Procesa un conjunto de atributos, filtrando primero los atributos con datos nulos o vacíos,
     * luego convirtiendo sus datos a minúsculas y generando sus IDs según la lógica establecida.
     * 
     * @param atributos El conjunto de atributos a procesar
     */
    public void procesarAtributos(Set<Atributo> atributos) {
        if (atributos != null) {
            // Primero filtrar atributos con datos nulos o vacíos para evitar problemas
            filtrarAtributosInvalidos(atributos);
            
            // Procesar solo los atributos válidos restantes
            for (Atributo atributo : atributos) {
                // Convertir el dato a minúsculas antes de generar el ID
                String datoOriginal = atributo.getDato();
                String datoLowerCase = datoOriginal.toLowerCase();
                atributo.setDato(datoLowerCase);
                log.debug("Dato convertido a minúsculas: {} -> {}", datoOriginal, datoLowerCase);
                
                String nuevoId = generarAtributoId(atributo);
                atributo.setId(nuevoId);
            }
        }
    }

    /**
     * Limpia los atributos huérfanos del sistema.
     * Elimina todos los nodos Atributo que caen en las siguientes categorías:
     * 1. Atributos que solo tienen relación con Filtro y ninguna otra
     * 2. Atributos que no tienen relación con Filtro
     * 3. Atributos completamente huérfanos (sin relaciones)
     * 
     * @return Mono<Integer> con el número total de atributos eliminados
     */
    public Mono<Integer> limpiarAtributosHuerfanos() {
        log.info("Iniciando limpieza completa de atributos huérfanos");
        
        return atributoRepository.eliminarAtributosSoloConFiltro()
                .doOnNext(count -> log.info("Eliminados {} atributos que solo tenían relación con Filtro", count))
                .flatMap(count1 -> 
                    atributoRepository.eliminarAtributosSinFiltro()
                        .doOnNext(count -> log.info("Eliminados {} atributos sin relación con Filtro", count))
                        .flatMap(count2 -> 
                            atributoRepository.eliminarAtributosCompletamenteHuerfanos()
                                .doOnNext(count -> log.info("Eliminados {} atributos completamente huérfanos", count))
                                .map(count3 -> {
                                    int totalEliminados = count1 + count2 + count3;
                                    log.info("Limpieza de atributos huérfanos completada. Total eliminados: {}", totalEliminados);
                                    return totalEliminados;
                                })
                        )
                )
                .doOnError(error -> log.error("Error al limpiar atributos huérfanos", error));
    }
}