package corp.jamaro.jamaroservidor.app.producto.controller;

import corp.jamaro.jamaroservidor.app.producto.model.CodigoFabrica;
import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.producto.model.Producto;
import corp.jamaro.jamaroservidor.app.producto.service.ProductoMantenimientoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Controller
@MessageMapping("producto-mantenimiento")
@RequiredArgsConstructor
@Slf4j
public class ProductoMantenimientoController {

    private final ProductoMantenimientoService productoMantenimientoService;

    /**
     * Endpoint para buscar un producto por su código exacto del sistema anterior (codProductoOld).
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "producto-mantenimiento.search.by-cod-producto-old" y un payload que contenga el código del producto.
     *
     * @param codProductoOld Código exacto del producto en el sistema anterior.
     * @return Mono con el Producto encontrado o vacío si no existe.
     */
    @MessageMapping("search.by-cod-producto-old")
    public Mono<Producto> buscarProductoPorCodProductoOld(String codProductoOld) {
        return productoMantenimientoService.buscarProductoPorCodProductoOld(codProductoOld);
    }

    /**
     * Endpoint para obtener un producto por su ID.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "producto-mantenimiento.get.by-id" y un payload que contenga el UUID del Producto.
     *
     * @param productoId UUID del Producto.
     * @return Mono con el Producto encontrado o vacío si no existe.
     */
    @MessageMapping("get.by-id")
    public Mono<Producto> obtenerProductoPorId(UUID productoId) {
        return productoMantenimientoService.obtenerProductoPorId(productoId);
    }

    /**
     * Endpoint para crear un nuevo Producto.
     * Recibe un Producto que el cliente ya construyó con un id asignado.
     * Comprueba que este aún no exista y procede a hacer un save.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "producto-mantenimiento.create" y un payload que contenga el Producto a crear.
     *
     * @param producto El Producto a crear.
     * @return Mono con el Producto creado o error si ya existe.
     */
    @MessageMapping("create")
    public Mono<Producto> crearProducto(Producto producto) {
        return productoMantenimientoService.crearProducto(producto);
    }

    /**
     * Endpoint para actualizar un Producto existente.
     * Valida que exista y luego usa transaccionalidad para:
     * - Actualizar los atributos del producto
     * - Actualizar las relaciones con vehículos
     * - Actualizar las relaciones con grupos
     * - Actualizar los códigos de fábrica
     * - Actualizar los archivos asociados
     * - Luego hacer el save
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "producto-mantenimiento.update" y un payload que contenga el Producto actualizado.
     *
     * @param productoActualizado El Producto con los datos actualizados.
     * @return Mono con el Producto actualizado.
     */
    @MessageMapping("update")
    public Mono<Producto> actualizarProducto(Producto productoActualizado) {
        return productoMantenimientoService.actualizarProducto(productoActualizado);
    }

    /**
     * Endpoint para buscar CodigoFabrica por su campo codigo usando una expresión regular que contenga cualquier coincidencia.
     * Utiliza RegexUtil.buildContainsAllRegex para construir la expresión regular de forma segura.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "producto-mantenimiento.search.codigo-fabrica-by-codigo" y un payload que contenga el texto a buscar.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Flux con los CodigoFabrica que cumplen el criterio.
     */
    @MessageMapping("search.codigo-fabrica-by-codigo")
    public Flux<CodigoFabrica> buscarCodigoFabricaPorCodigo(String userInput) {
        return productoMantenimientoService.buscarCodigoFabricaPorCodigo(userInput);
    }

    /**
     * Endpoint para buscar todos los Items que tienen relación con un Producto mediante codProductoOld.
     * Primero obtiene una lista simple de Items usando la consulta básica,
     * luego obtiene los Items completos con todas sus relaciones usando findById.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "producto-mantenimiento.search.items-by-cod-producto-old" y un payload que contenga el codProductoOld.
     *
     * @param codProductoOld Código del producto en el sistema anterior.
     * @return Flux con los Items completos relacionados con el producto.
     */
    @MessageMapping("search.items-by-cod-producto-old")
    public Flux<Item> buscarItemsPorCodProductoOld(String codProductoOld) {
        return productoMantenimientoService.buscarItemsPorCodProductoOld(codProductoOld);
    }
}
