package corp.jamaro.jamaroservidor.app.ventas.controller.gui;

import corp.jamaro.jamaroservidor.app.ventas.model.dto.SaleGuiDto;
import corp.jamaro.jamaroservidor.app.ventas.service.gui.SaleGuiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Controller
@RequiredArgsConstructor
@Slf4j
public class SaleGuiController {

    private final SaleGuiService saleGuiService;

    /**
     * Permite suscribirse a los cambios de un SaleGui.
     * Se espera recibir un UUID que representa el id del SaleGui.
     */
    @MessageMapping("saleGui.subscribe")
    public Flux<SaleGuiDto> subscribeToChanges(UUID saleGuiId) {
        log.info("Subscribing to SaleGui changes for id: {}", saleGuiId);
        return saleGuiService.subscribeToChanges(saleGuiId);
    }

    /**
     * Crea un nuevo SearchProductGui y lo añade al SaleGui.
     */
    @MessageMapping("saleGui.createSearchProduct")
    public Mono<Void> createSearchProduct(UUID saleGuiId) {
        log.info("Creating new SearchProduct for SaleGui with id: {}", saleGuiId);
        return saleGuiService.createNewSearchProduct(saleGuiId);
    }

    /**
     * Elimina un SearchProductGui del SaleGui.
     */
    @MessageMapping("saleGui.deleteSearchProduct")
    public Mono<Void> deleteSearchProduct(DeleteSearchProductRequest request) {
        log.info("Deleting SearchProduct with id: {} from SaleGui with id: {}",
                request.searchProductId(), request.saleGuiId());
        return saleGuiService.deleteSearchProduct(request.saleGuiId(), request.searchProductId());
    }

    /**
     * Elimina todos los SearchProductGui del SaleGui.
     */
    @MessageMapping("saleGui.deleteAllSearchProducts")
    public Mono<Void> deleteAllSearchProducts(UUID saleGuiId) {
        log.info("Deleting all SearchProducts from SaleGui with id: {}", saleGuiId);
        return saleGuiService.deleteAllSearchProducts(saleGuiId);
    }

    /**
     * Crea un nuevo SaleGui con un CollaborativeRoom asociado.
     */
    @MessageMapping("saleGui.create")
    public Mono<SaleGuiDto> createSaleGui() {
        return saleGuiService.createSaleGuiForCurrentUser();
    }

    public record DeleteSearchProductRequest(UUID saleGuiId, UUID searchProductId) {}
}
