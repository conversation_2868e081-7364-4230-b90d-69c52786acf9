package corp.jamaro.jamaroservidor.app.ventas.service.gui;


import corp.jamaro.jamaroservidor.app.model.collaborative.CollaborativeRoom;
import corp.jamaro.jamaroservidor.app.model.collaborative.repository.CollaborativeRoomRepository;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import corp.jamaro.jamaroservidor.app.ventas.model.dto.MainSaleGuiDto;
import corp.jamaro.jamaroservidor.app.ventas.model.dto.ToSaleGuiRelationDto;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.MainSaleGui;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.SaleGui;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.ToSaleGuiRelation;
import corp.jamaro.jamaroservidor.app.ventas.repository.gui.MainSaleGuiRepository;
import corp.jamaro.jamaroservidor.app.ventas.repository.gui.SaleGuiRepository;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class MainSaleGuiService {

    private final MainSaleGuiRepository mainSaleGuiRepository;
    private final SaleGuiRepository saleGuiRepository;
    private final CollaborativeRoomRepository collaborativeRoomRepository;

    // Sinks para notificar cambios en tiempo real (la entidad es la fuente de la verdad)
    private final ConcurrentHashMap<UUID, Sinks.Many<MainSaleGui>> sinks = new ConcurrentHashMap<>();

    /**
     * Obtiene o crea el Sink asociado a un MainSaleGui por su UUID.
     */
    private Sinks.Many<MainSaleGui> getOrCreateSink(UUID id) {
        return sinks.computeIfAbsent(id, __ -> Sinks.many().replay().latest());
    }

    // ─────────────────────────────────────────────────────────────────────────────
    // Métodos de conversión de entidades a DTOs (solo con los campos de interés)
    // ─────────────────────────────────────────────────────────────────────────────

    private MainSaleGuiDto toDto(MainSaleGui entity) {
        MainSaleGuiDto dto = new MainSaleGuiDto();
        dto.setId(entity.getId());
        dto.setUsernameOwner(entity.getUsernameOwner());
        if (entity.getSalesGui() != null) {
            Set<ToSaleGuiRelationDto> relations = entity.getSalesGui().stream()
                    .map(this::toDto)
                    .collect(Collectors.toSet());
            dto.setSalesGui(relations);
        }
        return dto;
    }

    private ToSaleGuiRelationDto toDto(ToSaleGuiRelation relation) {
        ToSaleGuiRelationDto dto = new ToSaleGuiRelationDto();
        if (relation.getSaleGui() != null) {
            dto.setSaleGuiId(relation.getSaleGui().getId());
        }
        dto.setOrdenPresentacion(relation.getOrdenPresentacion());
        dto.setDividerX(relation.getDividerX());
        dto.setDividerY(relation.getDividerY());
        dto.setAgregadoEl(relation.getAgregadoEl());
        // No need to set the relationship ID in the DTO as it's not used in the client
        return dto;
    }

    // ─────────────────────────────────────────────────────────────────────────────
    // Métodos expuestos al controller
    // ─────────────────────────────────────────────────────────────────────────────

    /**
     * Obtiene el MainSaleGui de un usuario por username y lo proyecta a DTO.
     * Utiliza la consulta Cypher optimizada para cargar la entidad con sus relaciones.
     */
    public Mono<MainSaleGuiDto> getMainSaleGuiOfUser(String username) {
        return mainSaleGuiRepository.findMainSaleGuiIdByUsername(username)
                .flatMap(mainSaleGuiRepository::findMainSaleGuiWithRelationsById)
                .map(this::toDto);
    }

    /**
     * Se suscribe a los cambios en un MainSaleGui (por UUID) y emite el DTO actualizado.
     * Utiliza la consulta Cypher optimizada para cargar la entidad con sus relaciones.
     */
    public Flux<MainSaleGuiDto> subscribeToChanges(UUID mainSaleGuiId) {
        return mainSaleGuiRepository.findMainSaleGuiWithRelationsById(mainSaleGuiId)
                .flatMapMany(entity -> {
                    var sink = getOrCreateSink(entity.getId());
                    return sink.asFlux().startWith(entity);
                })
                .map(this::toDto);
    }

    // ─────────────────────────────────────────────────────────────────────────────
    // Métodos internos para persistencia, emisión y manejo de concurrencia
    // ─────────────────────────────────────────────────────────────────────────────

    /**
     * Emite una versión fresca de la entidad cargada con Cypher para evitar problemas
     * de carga parcial de entidades. No guarda la entidad, solo emite.
     *
     * @param mainSaleGuiId El UUID de la entidad MainSaleGui a emitir
     * @return Mono<Void> que completa cuando la operación termina
     */
    private Mono<Void> emitOnce(UUID mainSaleGuiId) {
        return mainSaleGuiRepository.findMainSaleGuiWithRelationsById(mainSaleGuiId)
            .doOnSuccess(freshEntity -> {
                var sink = getOrCreateSink(freshEntity.getId());
                sink.tryEmitNext(freshEntity);
                log.info("Emitiendo actualización para MainSaleGui con id: {}", freshEntity.getId());
            })
            .then();
    }

    // Los métodos emitWithRetry y isConcurrencyException han sido eliminados por no ser necesarios

    // ─────────────────────────────────────────────────────────────────────────────
    // Lógica de orden de presentación (reordenación, verificación y asignación de posiciones)
    // ─────────────────────────────────────────────────────────────────────────────

    private int reorderByAgregadoEl(Set<ToSaleGuiRelation> relations) {
        List<ToSaleGuiRelation> sorted = relations.stream()
                .sorted(Comparator.comparing(ToSaleGuiRelation::getAgregadoEl))
                .toList();
        int i = 1;
        for (ToSaleGuiRelation rel : sorted) {
            rel.setOrdenPresentacion(i++);
        }
        return i;
    }

    private void reassignOrderByAgregadoEl(Set<ToSaleGuiRelation> relations) {
        reorderByAgregadoEl(relations);
    }

    private int checkAndFixOrder(Set<ToSaleGuiRelation> relations) {
        if (relations.isEmpty()) return 1;
        boolean anyInvalid = relations.stream()
                .anyMatch(rel -> rel.getOrdenPresentacion() == null || rel.getOrdenPresentacion() <= 0);
        if (anyInvalid) {
            return reorderByAgregadoEl(relations);
        }
        List<ToSaleGuiRelation> sorted = relations.stream()
                .sorted(Comparator.comparing(ToSaleGuiRelation::getOrdenPresentacion))
                .toList();
        int expected = 1;
        for (ToSaleGuiRelation rel : sorted) {
            if (!rel.getOrdenPresentacion().equals(expected)) {
                return reorderByAgregadoEl(relations);
            }
            expected++;
        }
        return expected;
    }

    // ─────────────────────────────────────────────────────────────────────────────
    // Operaciones principales sobre SaleGui
    // ─────────────────────────────────────────────────────────────────────────────

    public Mono<Void> createNewSaleGui(UUID mainSaleGuiId) {
        return mainSaleGuiRepository.findMainSaleGuiWithRelationsById(mainSaleGuiId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("MainSaleGui no encontrado")))
                .flatMap(mainSaleGui ->
                        SecurityUtils.getCurrentUser().flatMap(user -> {
                            // Crear un nuevo SaleGui con inicialización mínima
                            SaleGui saleGui = new SaleGui();
                            Sale sale= new Sale();
                            sale.setIniciadaPor(user);
                            saleGui.setSale(sale);

                            // Crear un nuevo CollaborativeRoom
                            CollaborativeRoom collaborativeRoom = new CollaborativeRoom();
                            collaborativeRoom.setIniciadaPor(user);

                            // Guardar el CollaborativeRoom primero
                            return collaborativeRoomRepository.save(collaborativeRoom)
                                .flatMap(savedRoom -> {
                                    // Luego guardar el SaleGui
                                    return saleGuiRepository.save(saleGui)
                                        .flatMap(savedSaleGui -> {
                                            // Establecer la relación entre SaleGui y CollaborativeRoom
                                            return saleGuiRepository.addCollaborativeRoomRelation(savedSaleGui.getId(), savedRoom.getId())
                                                .then(Mono.just(savedSaleGui));
                                        })
                                        .flatMap(savedSaleGui -> {
                                            // Crear la relación utilizando el modelo de datos
                                            ToSaleGuiRelation relation = new ToSaleGuiRelation();
                                            relation.setSaleGui(savedSaleGui);
                                            relation.setAgregadoEl(Instant.now());

                                            // Calcular el próximo orden de presentación
                                            int nextPos = checkAndFixOrder(mainSaleGui.getSalesGui());
                                            relation.setOrdenPresentacion(nextPos);

                                            // Agregar la relación al MainSaleGui
                                            if (mainSaleGui.getSalesGui() == null) {
                                                mainSaleGui.setSalesGui(new HashSet<>());
                                            }
                                            mainSaleGui.getSalesGui().add(relation);

                                            // Guardar la relación usando la consulta Cypher optimizada
                                            return mainSaleGuiRepository.addSaleGuiRelation(
                                                    mainSaleGuiId, savedSaleGui.getId(),
                                                    relation.getOrdenPresentacion(),
                                                    relation.getDividerX(), relation.getDividerY(),
                                                    relation.getAgregadoEl())
                                                    .then(emitOnce(mainSaleGuiId));
                                        });
                                });
                        })
                );
    }

    public Mono<Void> addSaleGui(UUID mainSaleGuiId, UUID saleGuiId) {
        return Mono.zip(
                mainSaleGuiRepository.findMainSaleGuiWithRelationsById(mainSaleGuiId)
                        .switchIfEmpty(Mono.error(new IllegalArgumentException("MainSaleGui no encontrado"))),
                saleGuiRepository.findById(saleGuiId)
                        .switchIfEmpty(Mono.error(new IllegalArgumentException("SaleGui no encontrado")))
        ).flatMap(tuple -> {
            MainSaleGui mainSaleGui = tuple.getT1();
            SaleGui existingSaleGui = tuple.getT2();

            // Crear la relación utilizando el modelo de datos
            ToSaleGuiRelation relation = new ToSaleGuiRelation();
            relation.setSaleGui(existingSaleGui);
            relation.setAgregadoEl(Instant.now());

            // Calcular el próximo orden de presentación
            int nextPos = checkAndFixOrder(mainSaleGui.getSalesGui());
            relation.setOrdenPresentacion(nextPos);

            // Agregar la relación al MainSaleGui
            if (mainSaleGui.getSalesGui() == null) {
                mainSaleGui.setSalesGui(new HashSet<>());
            }
            mainSaleGui.getSalesGui().add(relation);

            // Guardar la relación usando la consulta Cypher optimizada
            return mainSaleGuiRepository.addSaleGuiRelation(
                    mainSaleGuiId, existingSaleGui.getId(),
                    relation.getOrdenPresentacion(),
                    relation.getDividerX(), relation.getDividerY(),
                    relation.getAgregadoEl())
                    .then(emitOnce(mainSaleGuiId));
        });
    }

    public Mono<Void> removeSaleGui(UUID mainSaleGuiId, UUID saleGuiId) {
        return mainSaleGuiRepository.findMainSaleGuiWithRelationsById(mainSaleGuiId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("MainSaleGui no encontrado")))
                .flatMap(mainSaleGui -> {
                    // Verificar si el SaleGui existe en el MainSaleGui
                    boolean exists = mainSaleGui.getSalesGui().stream()
                            .anyMatch(rel -> rel.getSaleGui().getId().equals(saleGuiId));
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("SaleGui no encontrado en MainSaleGui"));
                    }

                    // Eliminar la relación usando la consulta Cypher optimizada
                    return mainSaleGuiRepository.removeSaleGuiRelation(mainSaleGuiId, saleGuiId)
                            // Reordenar las relaciones restantes
                            .then(Mono.defer(() -> {
                                // Obtener una versión actualizada del MainSaleGui
                                return mainSaleGuiRepository.findMainSaleGuiWithRelationsById(mainSaleGuiId)
                                        .flatMap(updatedMainSaleGui -> {
                                            // Reordenar las relaciones
                                            Set<ToSaleGuiRelation> relations = updatedMainSaleGui.getSalesGui();
                                            reassignOrderByAgregadoEl(relations);

                                            // Crear operaciones de actualización para cada relación
                                            List<Mono<Void>> updateOperations = new ArrayList<>();

                                            for (ToSaleGuiRelation rel : relations) {
                                                updateOperations.add(
                                                    mainSaleGuiRepository.updateOrdenPresentacion(
                                                        mainSaleGuiId, rel.getSaleGui().getId(), rel.getOrdenPresentacion())
                                                );
                                            }

                                            // Ejecutar todas las operaciones de actualización en paralelo
                                            return Flux.concat(updateOperations)
                                                    .then(emitOnce(mainSaleGuiId));
                                        });
                            }));
                });
    }

    /**
     * Elimina todas las relaciones SaleGui de un MainSaleGui.
     */
    public Mono<Void> removeAllSaleGuis(UUID mainSaleGuiId) {
        return mainSaleGuiRepository.findMainSaleGuiWithRelationsById(mainSaleGuiId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("MainSaleGui no encontrado")))
                .flatMap(mainSaleGui -> {
                    // Verificar si hay SaleGuis para eliminar
                    if (mainSaleGui.getSalesGui() == null || mainSaleGui.getSalesGui().isEmpty()) {
                        log.info("No hay SaleGuis para eliminar en MainSaleGui {}", mainSaleGuiId);
                        return Mono.empty();
                    }

                    log.info("Eliminando {} SaleGuis del MainSaleGui {}",
                            mainSaleGui.getSalesGui().size(), mainSaleGuiId);

                    // Eliminar todas las relaciones usando la consulta Cypher optimizada
                    return mainSaleGuiRepository.removeAllSaleGuiRelations(mainSaleGuiId)
                            .doOnSuccess(v -> log.info("Todas las relaciones SaleGui eliminadas del MainSaleGui {}", mainSaleGuiId))
                            .doOnError(e -> log.error("Error al eliminar relaciones SaleGui del MainSaleGui {}: {}",
                                    mainSaleGuiId, e.getMessage()))
                            // Finalmente emitir la actualización
                            .then(Mono.defer(() -> emitOnce(mainSaleGuiId)));
                });
    }

    public Mono<Void> reorderSaleGuis(UUID mainSaleGuiId, UUID saleGuiId, int newPosition) {
        return mainSaleGuiRepository.findMainSaleGuiWithRelationsById(mainSaleGuiId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("MainSaleGui no encontrado")))
                .flatMap(mainSaleGui -> {
                    // Verificar que la posición sea válida (mínimo 1)
                    if (newPosition < 1) {
                        return Mono.error(new IllegalArgumentException("La posición debe ser mayor o igual a 1"));
                    }

                    // Ordenar las relaciones por orden de presentación
                    List<ToSaleGuiRelation> sorted = mainSaleGui.getSalesGui().stream()
                            .sorted(Comparator.comparing(ToSaleGuiRelation::getOrdenPresentacion))
                            .toList();

                    // Buscar la relación objetivo
                    Optional<ToSaleGuiRelation> maybeRel = sorted.stream()
                            .filter(r -> r.getSaleGui().getId().equals(saleGuiId))
                            .findFirst();
                    if (maybeRel.isEmpty()) {
                        return Mono.error(new IllegalArgumentException("SaleGui no encontrado en MainSaleGui"));
                    }

                    // Obtener la posición actual
                    ToSaleGuiRelation target = maybeRel.get();
                    int currentPosition = target.getOrdenPresentacion();

                    // Si la posición es la misma, no hacer nada
                    if (currentPosition == newPosition) {
                        return Mono.empty();
                    }

                    // Crear una nueva lista para la reordenación
                    List<ToSaleGuiRelation> reordered = new ArrayList<>(sorted.size());

                    // Copiar todos los elementos excepto el objetivo
                    for (ToSaleGuiRelation rel : sorted) {
                        if (!rel.getSaleGui().getId().equals(saleGuiId)) {
                            reordered.add(rel);
                        }
                    }

                    // Ajustar la posición para asegurar que esté dentro de los límites
                    final int adjustedPos = Math.min(newPosition, reordered.size() + 1);

                    // Insertar el elemento objetivo en la posición ajustada
                    reordered.add(adjustedPos - 1, target);

                    // Asignar nuevos órdenes
                    List<Mono<Void>> updateOperations = new ArrayList<>();
                    for (int i = 0; i < reordered.size(); i++) {
                        ToSaleGuiRelation rel = reordered.get(i);
                        int newOrder = i + 1; // Posiciones empiezan en 1

                        // Actualizar el orden en la entidad
                        rel.setOrdenPresentacion(newOrder);

                        // Crear una operación de actualización para cada relación
                        updateOperations.add(
                            mainSaleGuiRepository.updateOrdenPresentacion(
                                mainSaleGuiId, rel.getSaleGui().getId(), newOrder)
                        );
                    }

                    // Ejecutar todas las operaciones de actualización secuencialmente
                    // para garantizar el orden correcto
                    return Flux.concat(updateOperations)
                            .then(emitOnce(mainSaleGuiId));
                });
    }
}
