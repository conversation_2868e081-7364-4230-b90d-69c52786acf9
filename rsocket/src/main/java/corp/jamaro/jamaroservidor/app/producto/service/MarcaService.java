package corp.jamaro.jamaroservidor.app.producto.service;

import corp.jamaro.jamaroservidor.app.producto.model.Marca;
import corp.jamaro.jamaroservidor.app.producto.repository.MarcaRepository;
import corp.jamaro.jamaroservidor.app.util.RegexUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Slf4j
public class MarcaService {

    private final MarcaRepository marcaRepository;

    /**
     * Busca Marcas por su campo nombre usando buildContainsAllRegex de RegexUtil.
     * Utiliza una expresión regular que contenga todas las palabras del input.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Flux con las Marcas que cumplen el criterio de búsqueda.
     */
    public Flux<Marca> buscarMarcaPorNombre(String userInput) {
        log.info("Buscando Marca por nombre con regex: {}", userInput);
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return marcaRepository.findByNombreRegex(regex)
                .doOnNext(marca -> log.debug("Marca encontrada: {} - {}", marca.getAbreviacion(), marca.getNombre()));
    }

    /**
     * Crea una nueva Marca.
     * Verifica que no exista una Marca con la misma abreviacion antes de crear.
     *
     * @param marca La Marca a crear.
     * @return Mono con la Marca creada o error si ya existe.
     */
    public Mono<Marca> crearMarca(Marca marca) {
        log.info("Creando nueva Marca con abreviacion: {}", marca.getAbreviacion());
        
        if (marca.getAbreviacion() == null || marca.getAbreviacion().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("La abreviacion de la Marca no puede ser nula o vacía"));
        }
        
        // Verificar que no exista una Marca con la misma abreviacion
        return marcaRepository.existsById(marca.getAbreviacion())
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new IllegalArgumentException("Ya existe una Marca con la abreviacion: " + marca.getAbreviacion()));
                    }
                    return marcaRepository.save(marca);
                })
                .doOnSuccess(marcaCreada -> log.info("Marca creada exitosamente: {} - {}", 
                        marcaCreada.getAbreviacion(), marcaCreada.getNombre()));
    }

    /**
     * Actualiza una Marca existente.
     * Valida que la Marca exista antes de actualizarla.
     *
     * @param marcaActualizada La Marca con los datos actualizados.
     * @return Mono con la Marca actualizada.
     */
    public Mono<Marca> actualizarMarca(Marca marcaActualizada) {
        log.info("Actualizando Marca con abreviacion: {}", marcaActualizada.getAbreviacion());
        
        if (marcaActualizada.getAbreviacion() == null || marcaActualizada.getAbreviacion().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("La abreviacion de la Marca no puede ser nula o vacía"));
        }
        
        // Verificar que la Marca exista
        return marcaRepository.findById(marcaActualizada.getAbreviacion())
                .switchIfEmpty(Mono.error(new IllegalArgumentException("No existe una Marca con la abreviacion: " + marcaActualizada.getAbreviacion())))
                .flatMap(marcaExistente -> {
                    // Actualizar la Marca
                    return marcaRepository.save(marcaActualizada);
                })
                .doOnSuccess(marcaActualizadaGuardada -> log.info("Marca actualizada exitosamente: {} - {}", 
                        marcaActualizadaGuardada.getAbreviacion(), marcaActualizadaGuardada.getNombre()));
    }

    /**
     * Obtiene una Marca por su abreviacion (ID).
     *
     * @param abreviacion La abreviacion de la Marca.
     * @return Mono con la Marca encontrada o vacío si no existe.
     */
    public Mono<Marca> obtenerMarcaPorAbreviacion(String abreviacion) {
        log.info("Obteniendo Marca por abreviacion: {}", abreviacion);
        return marcaRepository.findById(abreviacion);
    }

    /**
     * Obtiene todas las Marcas.
     *
     * @return Flux con todas las Marcas.
     */
    public Flux<Marca> obtenerTodasLasMarcas() {
        log.info("Obteniendo todas las Marcas");
        return marcaRepository.findAll();
    }
}
