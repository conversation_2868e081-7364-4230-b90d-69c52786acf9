package corp.jamaro.jamaroservidor.app.producto.controller;

import corp.jamaro.jamaroservidor.app.producto.model.Marca;
import corp.jamaro.jamaroservidor.app.producto.service.MarcaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Controller
@MessageMapping("marca")
@RequiredArgsConstructor
@Slf4j
public class MarcaController {

    private final MarcaService marcaService;

    /**
     * Endpoint para buscar Marcas por su campo nombre usando buildContainsAllRegex de RegexUtil.
     * Utiliza una expresión regular que contenga todas las palabras del input.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "marca.search.by-nombre" y un payload que contenga el texto a buscar.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Flux con las Marcas que cumplen el criterio de búsqueda.
     */
    @MessageMapping("search.by-nombre")
    public Flux<Marca> buscarMarcaPorNombre(String userInput) {
        return marcaService.buscarMarcaPorNombre(userInput);
    }

    /**
     * Endpoint para crear una nueva Marca.
     * Verifica que no exista una Marca con la misma abreviacion antes de crear.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "marca.create" y un payload que contenga la Marca a crear.
     *
     * @param marca La Marca a crear.
     * @return Mono con la Marca creada o error si ya existe.
     */
    @MessageMapping("create")
    public Mono<Marca> crearMarca(Marca marca) {
        return marcaService.crearMarca(marca);
    }

    /**
     * Endpoint para actualizar una Marca existente.
     * Valida que la Marca exista antes de actualizarla.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "marca.update" y un payload que contenga la Marca actualizada.
     *
     * @param marcaActualizada La Marca con los datos actualizados.
     * @return Mono con la Marca actualizada.
     */
    @MessageMapping("update")
    public Mono<Marca> actualizarMarca(Marca marcaActualizada) {
        return marcaService.actualizarMarca(marcaActualizada);
    }

    /**
     * Endpoint para obtener una Marca por su abreviacion (ID).
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "marca.get.by-abreviacion" y un payload que contenga la abreviacion.
     *
     * @param abreviacion La abreviacion de la Marca.
     * @return Mono con la Marca encontrada o vacío si no existe.
     */
    @MessageMapping("get.by-abreviacion")
    public Mono<Marca> obtenerMarcaPorAbreviacion(String abreviacion) {
        return marcaService.obtenerMarcaPorAbreviacion(abreviacion);
    }

    /**
     * Endpoint para obtener todas las Marcas.
     *
     * Ejemplo de llamada:
     * Enviar mensaje con la ruta "marca.get.all" sin payload.
     *
     * @return Flux con todas las Marcas.
     */
    @MessageMapping("get.all")
    public Flux<Marca> obtenerTodasLasMarcas() {
        return marcaService.obtenerTodasLasMarcas();
    }
}
