package corp.jamaro.jamaroservidor.app.ventas.controller.gui;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.producto.model.Producto;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.FiltroDatoRellenado;
import corp.jamaro.jamaroservidor.app.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroservidor.app.ventas.service.gui.SearchProductGuiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Controller
@RequiredArgsConstructor
@Slf4j
@MessageMapping("searchProductGui")
public class SearchProductGuiController {

    private final SearchProductGuiService service;

    @MessageMapping("subscribe")
    public Flux<SearchProductGui> subscribe(@Payload UUID id) {
        log.info("Suscribiéndose a cambios de SearchProductGui con id: {}", id);
        return service.subscribeToChanges(id);
    }

    @MessageMapping("update.vehiculoSearch")
    public Mono<SearchProductGui> updateVehiculoSearch(@Payload UpdateVehiculoSearchRequest request) {
        log.info("Actualizando vehiculoSearch de SearchProductGui con id: {}", request.id());
        return service.updateVehiculoSearch(request.id(), request.vehiculoSearch());
    }

    @MessageMapping("update.grupo")
    public Mono<SearchProductGui> updateGrupo(@Payload UpdateGrupoRequest request) {
        log.info("Actualizando grupo de SearchProductGui con id: {} a idGrupo: {}", request.id(), request.idGrupo());
        return service.updateGrupo(request.id(), request.idGrupo());
    }

    @MessageMapping("duplicate.filtro-group-row")
    public Mono<SearchProductGui> duplicateFiltroGroupRow(@Payload DuplicateFiltroGroupRowRequest request) {
        log.info("Duplicando fila {} en SearchProductGui con id: {}", request.rowIndex(), request.id());
        return service.duplicateFiltroGroupRow(request.id(), request.rowIndex());
    }

    @MessageMapping("delete.filtro-group-row")
    public Mono<SearchProductGui> deleteFiltroGroupRow(@Payload DeleteFiltroGroupRowRequest request) {
        log.info("Eliminando fila {} en SearchProductGui con id: {}", request.rowIndex(), request.id());
        return service.deleteFiltroGroupRow(request.id(), request.rowIndex());
    }

    /**
     * Endpoint para actualizar un filtro específico en un SearchProductGui.
     * Este endpoint unificado permite actualizar tanto el dato como el tipo de búsqueda
     * del filtro en una sola operación, recibiendo el objeto FiltroDatoRellenado completo.
     *
     * @param request Solicitud que contiene el ID del SearchProductGui y el FiltroDatoRellenado a actualizar
     * @return Mono con el SearchProductGui actualizado
     */
    @MessageMapping("update.filtro-dato")
    public Mono<SearchProductGui> updateFiltroDato(@Payload UpdateFiltroDatoRequest request) {
        log.info("Actualizando filtro en fila {} y columna {} de SearchProductGui con id: {} - Dato: '{}', TipoBusqueda: {}",
                request.filtroDatoRellenado().getFila(), request.filtroDatoRellenado().getColumna(), request.id(),
                request.filtroDatoRellenado().getDato(), request.filtroDatoRellenado().getTipoBusqueda());
        return service.updateFiltroDato(request.id(), request.filtroDatoRellenado());
    }

    @MessageMapping("getProductSearched")
    public Flux<Producto> getProductSearched(@Payload SearchProductGui spg) {
        log.info("Buscando productos según criterios de SearchProductGui: {}", spg);
        return service.getProductSearched(spg);
    }

    @MessageMapping("getItemsByProductoId")
    public Flux<Item> getItemsByProductoId(@Payload UUID productoId) {
        log.info("Obteniendo Items para Producto con id: {}", productoId);
        return service.getItemsByProductoId(productoId);
    }

    /**
     * Endpoint para obtener sugerencias de descripción basado en el SearchProductGui.
     * Si spg.idGrupo está definido, se filtra para retornar solo las descripciones de los productos que pertenecen a ese grupo.
     *
     * @param spg SearchProductGui con los filtros de búsqueda.
     * @return Flux con hasta 30 descripciones sugeridas.
     */
    @MessageMapping("productoDescripcionSugerencias")
    public Flux<String> productoDescripcionSugerencias(@Payload SearchProductGui spg) {
        log.info("Recibido SearchProductGui para sugerencias de descripción: {}", spg);
        return service.productoDescripcionSugerencias(spg);
    }

    @MessageMapping("update.comodines")
    public Mono<SearchProductGui> updateComodines(@Payload SearchProductGui spg) {
        log.info("Actualizando comodines en SearchProductGui con id: {}", spg.getId());
        return service.updateComodines(spg);
    }

    /**
     * Crea un nuevo SearchProductGui y se suscribe automáticamente a él.
     * Este endpoint está diseñado para búsquedas rápidas que no tienen relación con un SaleGui.
     * 
     * @return Flux que emite el SearchProductGui inicial y luego las actualizaciones
     */
    @MessageMapping("createAndSubscribe")
    public Flux<SearchProductGui> createAndSubscribe() {
        log.info("Creando y suscribiéndose a nuevo SearchProductGui para búsqueda rápida");
        return service.createAndSubscribe();
    }

    /**
     * Cierra la suscripción y elimina el SearchProductGui junto con sus FiltroDatoRellenado.
     * Este endpoint está diseñado para búsquedas rápidas que no tienen relación con un SaleGui
     * y deben ser eliminadas después de cumplir su función para evitar dejarlas huérfanas.
     * 
     * @param request Solicitud con el ID del SearchProductGui a eliminar
     * @return Mono que se completa cuando la eliminación es exitosa
     */
    @MessageMapping("closeAndDelete")
    public Mono<Void> closeAndDelete(@Payload CloseAndDeleteRequest request) {
        log.info("Cerrando y eliminando SearchProductGui con id: {}", request.id());
        return service.closeAndDelete(request.id());
    }

    // Records para agrupar los parámetros de cada solicitud.
    public static record UpdateVehiculoSearchRequest(UUID id, String vehiculoSearch) {}
    public static record UpdateGrupoRequest(UUID id, String idGrupo) {}
    public static record DuplicateFiltroGroupRowRequest(UUID id, int rowIndex) {}
    public static record DeleteFiltroGroupRowRequest(UUID id, int rowIndex) {}
    public static record UpdateFiltroDatoRequest(UUID id, FiltroDatoRellenado filtroDatoRellenado) {}
    public static record CloseAndDeleteRequest(UUID id) {}
}
