package corp.jamaro.jamaroservidor.app.producto.service;

import corp.jamaro.jamaroservidor.app.producto.model.CodigoFabrica;
import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.producto.model.Producto;
import corp.jamaro.jamaroservidor.app.producto.model.enums.TipoGrupo;
import corp.jamaro.jamaroservidor.app.producto.repository.CodigoFabricaRepository;
import corp.jamaro.jamaroservidor.app.producto.repository.ItemRepository;
import corp.jamaro.jamaroservidor.app.producto.repository.ProductoRepository;
import corp.jamaro.jamaroservidor.app.util.AtributoUtil;
import corp.jamaro.jamaroservidor.app.util.RegexUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductoMantenimientoService {
    private final TransactionalOperator transactionalOperator;
    private final ProductoRepository productoRepository;
    private final CodigoFabricaRepository codigoFabricaRepository;
    private final ItemRepository itemRepository;
    private final AtributoUtil atributoUtil;

    /**
     * Valida que el producto tenga relación con solo un Grupo del tipo categoria.
     * 
     * @param producto El producto a validar
     * @throws IllegalArgumentException si el producto tiene más de una relación con grupos de tipo categoria
     */
    private void validarRelacionConGruposCategoria(Producto producto) {
        if (producto.getGrupos() == null) {
            return; // No hay grupos, no hay nada que validar
        }
        
        long cantidadGruposCategoria = producto.getGrupos().stream()
                .filter(grupo -> grupo.getTipo() == TipoGrupo.categoria)
                .count();
        
        if (cantidadGruposCategoria > 1) {
            throw new IllegalArgumentException(
                String.format("El producto no puede tener más de una relación con grupos del tipo categoria. " +
                             "Se encontraron %d grupos de tipo categoria.", cantidadGruposCategoria)
            );
        }
        
        log.debug("Validación de grupos categoria completada. Grupos categoria encontrados: {}", cantidadGruposCategoria);
    }

    /**
     * Asigna un codProductoOld al producto si es nulo o vacío.
     * Obtiene el máximo codProductoOld existente en la base de datos y le suma 1.
     * Si no existen códigos previos, asigna "1".
     * 
     * @param producto El producto al que se le asignará el código si es necesario
     * @return Un Mono con el producto que puede tener un nuevo codProductoOld asignado
     */
    private Mono<Producto> asignarCodProductoOldSiEsNecesario(Producto producto) {
        // Si codProductoOld ya tiene un valor, no hacer nada
        if (producto.getCodProductoOld() != null && !producto.getCodProductoOld().trim().isEmpty()) {
            log.debug("El producto ya tiene codProductoOld: {}", producto.getCodProductoOld());
            return Mono.just(producto);
        }
        
        log.info("Asignando codProductoOld automáticamente para el producto con id: {}", producto.getId());
        
        // Obtener el máximo codProductoOld y asignar el siguiente
        return productoRepository.findMaxCodProductoOldAsInteger()
                .defaultIfEmpty(0) // Si no hay códigos previos, empezar desde 0
                .map(maxCod -> {
                    String nuevoCodProductoOld = String.valueOf(maxCod + 1);
                    producto.setCodProductoOld(nuevoCodProductoOld);
                    log.info("Asignado codProductoOld: {} al producto con id: {}", nuevoCodProductoOld, producto.getId());
                    return producto;
                });
    }

    /**
     * Busca un producto por su código exacto del sistema anterior (codProductoOld).
     *
     * @param codProductoOld Código exacto del producto en el sistema anterior.
     * @return Un Mono con el Producto encontrado o vacío si no existe.
     */
    public Mono<Producto> buscarProductoPorCodProductoOld(String codProductoOld) {
        // Log the original input code for debugging
        log.info("Buscando Producto por codProductoOld exacto: {}", codProductoOld);

        // Initialize variable with the input code
        String paddedCodProductoOld = codProductoOld;

        // Check if the input is not null and has fewer than 5 digits
        if (codProductoOld != null && codProductoOld.length() < 5) {
            // Convert to integer and format with leading zeros to ensure 5 digits
            // For example, "32" becomes "00032", "1956" becomes "01956"
            paddedCodProductoOld = String.format("%05d", Integer.parseInt(codProductoOld));
            // Log the transformed code for traceability
            log.info("Código transformado a: {}", paddedCodProductoOld);
        }
        // If the code has 5 or more digits, it remains unchanged

        // Query the repository with the (possibly) padded code
        return productoRepository.findByCodProductoOld(paddedCodProductoOld);
    }

    /**
     * Obtiene un producto por su ID.
     *
     * @param productoId UUID del Producto.
     * @return Un Mono con el Producto encontrado o vacío si no existe.
     */
    public Mono<Producto> obtenerProductoPorId(UUID productoId) {
        log.info("Obteniendo Producto por id: {}", productoId);
        return productoRepository.findById(productoId);
    }

    /**
     * Crea un nuevo Producto. Recibe un Producto que el cliente ya construyó 
     * con un id asignado. Comprueba que este aún no exista y procede a hacer un save.
     * Procesa los atributos del producto para generar sus IDs según la lógica establecida.
     * Si codProductoOld es nulo o vacío, asigna automáticamente el siguiente número secuencial.
     *
     * @param producto El Producto a crear.
     * @return Un Mono con el Producto creado o error si ya existe.
     */
    public Mono<Producto> crearProducto(Producto producto) {
        log.info("Creando nuevo Producto con id: {}", producto.getId());

        // Procesar atributos para generar sus IDs
        atributoUtil.procesarAtributos(producto.getAtributos());

        // Validar que el producto tenga relación con solo un Grupo del tipo categoria
        validarRelacionConGruposCategoria(producto);

        // Asignar codProductoOld si es nulo o vacío
        return asignarCodProductoOldSiEsNecesario(producto)
                .flatMap(productoConCodigo -> {
                    // Verificar que no exista
                    return productoRepository.existsById(producto.getId())
                            .flatMap(exists -> {
                                if (exists) {
                                    return Mono.error(new IllegalArgumentException("Ya existe un Producto con el id: " + producto.getId()));
                                }
                                return productoRepository.save(productoConCodigo);
                            });
                })
                .flatMap(productoCreado -> {
                    // Limpiar atributos huérfanos después de crear el producto
                    return atributoUtil.limpiarAtributosHuerfanos()
                            .doOnNext(count -> log.debug("Limpieza de atributos huérfanos completada después de crear producto. Eliminados: {}", count))
                            .then(Mono.just(productoCreado));
                });
    }

    /**
     * Actualiza un Producto existente. Valida que exista.
     * Procesa los atributos del producto para generar sus IDs según la lógica establecida.
     * Usa transaccionalidad y varios métodos internos para actualizar de manera eficiente:
     * - Actualiza los atributos del producto
     * - Actualiza las relaciones con vehículos
     * - Actualiza las relaciones con grupos
     * - Actualiza los códigos de fábrica
     * - Actualiza los archivos asociados
     * - Luego hace el save
     *
     * @param productoActualizado El Producto con los datos actualizados.
     * @return Un Mono con el Producto actualizado.
     */
    public Mono<Producto> actualizarProducto(Producto productoActualizado) {
        log.info("Actualizando Producto con id: {}", productoActualizado.getId());

        // Procesar atributos para generar sus IDs
        atributoUtil.procesarAtributos(productoActualizado.getAtributos());

        // Validar que el producto tenga relación con solo un Grupo del tipo categoria
        validarRelacionConGruposCategoria(productoActualizado);

        return transactionalOperator.transactional(
                productoRepository.findById(productoActualizado.getId())
                        .switchIfEmpty(Mono.error(new IllegalArgumentException("No existe un Producto con el id: " + productoActualizado.getId())))
                        .flatMap(productoExistente -> {
                            // Actualizar el producto directamente
                            // Las relaciones se actualizan automáticamente por Spring Data Neo4j
                            return productoRepository.save(productoActualizado);
                        })
                        .flatMap(productoActualizadoGuardado -> {
                            // Limpiar atributos huérfanos después de actualizar el producto
                            return atributoUtil.limpiarAtributosHuerfanos()
                                    .doOnNext(count -> log.debug("Limpieza de atributos huérfanos completada después de actualizar producto. Eliminados: {}", count))
                                    .then(Mono.just(productoActualizadoGuardado));
                        })
        );
    }

    /**
     * Busca CodigoFabrica por su campo codigo usando una expresión regular que contenga cualquier coincidencia.
     * Utiliza RegexUtil.buildContainsAllRegex para construir la expresión regular de forma segura.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Un Flux que emite los CodigoFabrica que cumplen el criterio.
     */
    public Flux<CodigoFabrica> buscarCodigoFabricaPorCodigo(String userInput) {
        log.info("Buscando CodigoFabrica por codigo con regex: {}", userInput);
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return codigoFabricaRepository.findByCodigoRegex(regex)
                .doOnNext(codigoFabrica -> log.info("CodigoFabrica encontrado: {}", codigoFabrica.getCodigo()));
    }

    /**
     * Busca todos los Items que tienen relación con un Producto mediante codProductoOld.
     * Primero obtiene una lista simple de Items usando la consulta básica,
     * luego obtiene los Items completos con todas sus relaciones usando findById.
     *
     * @param codProductoOld Código del producto en el sistema anterior.
     * @return Un Flux que emite los Items completos relacionados con el producto.
     */
    public Flux<Item> buscarItemsPorCodProductoOld(String codProductoOld) {
        log.info("Buscando Items por codProductoOld: {}", codProductoOld);
        
        return itemRepository.findByCodProductoOld(codProductoOld)
                .doOnNext(item -> log.debug("Item básico encontrado: {}", item.getCodCompuesto()))
                .flatMap(itemBasico -> {
                    // Obtener el Item completo con todas sus relaciones usando findByCodCompuesto
                    return itemRepository.findByCodCompuesto(itemBasico.getCodCompuesto())
                            .doOnNext(itemCompleto -> log.debug("Item completo obtenido: {}", itemCompleto.getCodCompuesto()));
                })
                .doOnComplete(() -> log.info("Búsqueda de Items por codProductoOld completada: {}", codProductoOld));
    }
}
