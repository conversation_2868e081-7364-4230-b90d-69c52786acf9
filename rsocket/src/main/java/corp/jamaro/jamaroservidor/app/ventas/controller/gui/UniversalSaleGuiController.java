package corp.jamaro.jamaroservidor.app.ventas.controller.gui;

import corp.jamaro.jamaroservidor.app.ventas.model.dto.UniversalSaleGuiDto;
import corp.jamaro.jamaroservidor.app.ventas.service.gui.UniversalSaleGuiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Controller RSocket para exponer las operaciones relacionadas con UniversalSaleGui (en forma de DTO).
 *
 * Endpoints expuestos:
 * <ul>
 *   <li><b>universalSaleGui.subscribe</b>: Se suscribe a los cambios de la GUI (payload: UUID del usuario).</li>
 *   <li><b>universalSaleGui.saveDto</b>: Guarda (o actualiza) el UniversalSaleGui usando un DTO.</li>
 *   <li><b>universalSaleGui.addAuditedMainSale</b>: Agrega un MainSaleGui a la auditoría.</li>
 *   <li><b>universalSaleGui.removeAuditedMainSale</b>: Remueve un MainSaleGui de la auditoría.</li>
 *   <li><b>universalSaleGui.saveGuiConfig</b>: Actualiza la configuración de la GUI.</li>
 * </ul>
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class UniversalSaleGuiController {

    private final UniversalSaleGuiService universalSaleGuiService;

    /**
     * Permite suscribirse a los cambios de la UniversalSaleGui del usuario,
     * retornando un DTO con los datos esenciales.
     *
     * @param userId Identificador del usuario.
     * @return Flux que emite el UniversalSaleGuiDto actual y sus actualizaciones.
     */
    @MessageMapping("universalSaleGui.subscribe")
    public Flux<UniversalSaleGuiDto> subscribeToChanges(UUID userId) {
        log.info("Subscribing to UniversalSaleGui changes for userId: {}", userId);
        return universalSaleGuiService.subscribeToChanges(userId);
    }



    /**
     * Agrega el id de un MainSaleGui a la lista de auditoría de la UniversalSaleGui,
     * retornando el DTO resultante.
     */
    @MessageMapping("universalSaleGui.addAuditedMainSale")
    public Mono<UniversalSaleGuiDto> addAuditedMainSale(AddAuditedMainSaleRequest request) {
        log.info("Adding audited MainSaleGui: universalSaleGuiId={}, mainSaleGuiId={}",
                request.universalSaleGuiId(), request.mainSaleGuiId());
        return universalSaleGuiService.addAuditedMainSale(request.universalSaleGuiId(), request.mainSaleGuiId());
    }

    /**
     * Remueve el id de un MainSaleGui de la lista de auditoría,
     * retornando el DTO resultante.
     */
    @MessageMapping("universalSaleGui.removeAuditedMainSale")
    public Mono<UniversalSaleGuiDto> removeAuditedMainSale(RemoveAuditedMainSaleRequest request) {
        log.info("Removing audited MainSaleGui: universalSaleGuiId={}, mainSaleGuiId={}",
                request.universalSaleGuiId(), request.mainSaleGuiId());
        return universalSaleGuiService.removeAuditedMainSale(request.universalSaleGuiId(), request.mainSaleGuiId());
    }

    /**
     * Actualiza la configuración (guiConfig) de la UniversalSaleGui (usuario actual),
     * retornando el DTO final.
     */
    @MessageMapping("universalSaleGui.saveGuiConfig")
    public Mono<UniversalSaleGuiDto> saveGuiConfig(String json) {
        log.info("Saving GUI configuration: {}", json);
        return universalSaleGuiService.saveGuiConfig(json);
    }



    /**
     * Record para la petición de agregar un MainSaleGui a la auditoría.
     */
    public record AddAuditedMainSaleRequest(UUID universalSaleGuiId, UUID mainSaleGuiId) {}

    /**
     * Record para la petición de remover un MainSaleGui de la auditoría.
     */
    public record RemoveAuditedMainSaleRequest(UUID universalSaleGuiId, UUID mainSaleGuiId) {}
}
