package corp.jamaro.jamaroservidor.app.service;

import corp.jamaro.jamaroservidor.app.model.Cliente;
import corp.jamaro.jamaroservidor.app.repository.ClienteRepository;
import corp.jamaro.jamaroservidor.app.util.RegexUtil;
import corp.jamaro.jamaroservidor.security.enums.RoleEnum;
import corp.jamaro.jamaroservidor.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.UUID;

/**
 * Servicio para gestionar las operaciones relacionadas con los clientes.
 * Este servicio implementa el patrón de comunicación RSocket donde:
 * 1. El cliente envía solicitudes de búsqueda
 * 2. El servidor procesa las solicitudes y devuelve los resultados
 * 3. El cliente puede suscribirse a actualizaciones de clientes específicos
 *
 * CAMPOS CON RESTRICCIONES ADMINISTRATIVAS:
 * Los campos tieneCredito=true, esMayorista=true, esProveedor=true y estado=false
 * requieren roles ADMIN, MANAGER o ADMINISTRATOR para ser modificados.
 *
 * PATRÓN TRANSACCIONAL:
 * Utiliza TransactionalOperator para operaciones delicadas siguiendo el patrón:
 * - Validaciones fuera de la transacción
 * - Operaciones de modificación dentro del bloque transaccional
 * - Emisión de actualizaciones después de la transacción exitosa
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ClienteService {

    private final ClienteRepository clienteRepository;

    // TransactionalOperator autoconfigurado por Spring Boot para operaciones críticas
    // que requieren consistencia transaccional en la base de datos Neo4j
    private final TransactionalOperator transactionalOperator;

    // Emisor para notificar actualizaciones vía RSocket
    // Utiliza multicast.directBestEffort() para enviar actualizaciones a múltiples suscriptores
    // sin bloquear si algún suscriptor es lento
    private final Sinks.Many<Cliente> updateSink = Sinks.many().multicast().directBestEffort();

    /**
     * Permite a los clientes suscribirse a actualizaciones de un Cliente específico.
     *
     * El flujo emitirá:
     * 1. El estado actual del Cliente como primer elemento
     * 2. Todas las actualizaciones futuras que se realicen sobre ese Cliente
     *
     * @param clienteId ID del Cliente al que se quiere suscribir
     * @return Flux que emite el Cliente actual y sus actualizaciones futuras
     */
    public Flux<Cliente> subscribeToClienteUpdates(UUID clienteId) {
        log.info("Suscribiendo a actualizaciones del Cliente con ID: {}", clienteId);

        // Primero obtenemos el estado actual del Cliente
        Mono<Cliente> currentState = clienteRepository.findById(clienteId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Cliente no encontrado con ID: " + clienteId)));

        // Luego nos suscribimos al flujo de actualizaciones, filtrando solo las del Cliente solicitado
        Flux<Cliente> updates = updateSink.asFlux()
                .filter(cliente -> cliente.getId().equals(clienteId));

        // Concatenamos el estado actual con las actualizaciones futuras
        return currentState.concatWith(updates);
    }

    /**
     * Busca clientes usando expresión regular en nombre, apellido o razónSocial.
     * Búsqueda insensible a mayúsculas y minúsculas, sin importar posición.
     * Limitado a 30 resultados para optimizar rendimiento.
     *
     * @param searchTerm Término de búsqueda para nombre, apellido o razónSocial
     * @return Flux de hasta 30 clientes que coinciden con la búsqueda
     */
    public Flux<Cliente> searchClientesByNameRegex(String searchTerm) {
        log.debug("Buscando clientes por nombre/apellido/razónSocial con término: {}", searchTerm);

        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            log.debug("Término de búsqueda vacío, retornando flujo vacío");
            return Flux.empty();
        }

        // Construir expresión regular usando RegexUtil para búsqueda que contenga todas las palabras
        String regex = RegexUtil.buildContainsAllRegex(searchTerm.trim());
        log.debug("Expresión regular generada: {}", regex);

        return clienteRepository.findByNameRegex(regex)
                .doOnNext(cliente -> log.debug("Cliente encontrado: {} - {}",
                    cliente.getNombreApellidos(), cliente.getRazonSocial()))
                .onErrorResume(e -> {
                    log.error("Error al buscar clientes por nombre: {}", e.getMessage());
                    return Flux.empty();
                });
    }

    /**
     * Busca clientes por coincidencia exacta en dni, ruc o otroDocumento.
     * Búsqueda insensible a mayúsculas y minúsculas.
     *
     * @param document Documento a buscar (dni, ruc o otroDocumento)
     * @return Flux de clientes que coinciden exactamente con el documento
     */
    public Flux<Cliente> searchClientesByDocumentExact(String document) {
        log.debug("Buscando clientes por documento exacto: {}", document);

        if (document == null || document.trim().isEmpty()) {
            log.debug("Documento de búsqueda vacío, retornando flujo vacío");
            return Flux.empty();
        }

        // Construir expresión regular usando RegexUtil para coincidencia exacta
        String regex = RegexUtil.buildExactMatchRegex(document.trim());
        log.debug("Expresión regular generada para documento: {}", regex);

        return clienteRepository.findByDocumentExact(regex)
                .doOnNext(cliente -> log.debug("Cliente encontrado por documento: {} - DNI: {}, RUC: {}, Otro: {}",
                    cliente.getNombreApellidos(), cliente.getDni(), cliente.getRuc(), cliente.getOtroDocumento()))
                .onErrorResume(e -> {
                    log.error("Error al buscar clientes por documento: {}", e.getMessage());
                    return Flux.empty();
                });
    }

    /**
     * Crea un nuevo cliente en la base de datos.
     *
     * LÓGICA DE NEGOCIO:
     * 1. Valida que el nombre del cliente sea requerido
     * 2. Verifica autorización para campos administrativos privilegiados
     * 3. Establece valores por defecto para campos booleanos
     * 4. Persiste el cliente en la base de datos
     * 5. Emite actualización a suscriptores vía RSocket
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Los campos tieneCredito=true, esMayorista=true, esProveedor=true y estado=false
     * requieren roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param cliente Cliente a crear
     * @return Mono que emite el Cliente creado con su ID generado
     */
    public Mono<Cliente> createCliente(Cliente cliente) {
        log.info("Creando nuevo cliente: {}", cliente.getNombreApellidos());

        // Validaciones básicas fuera de la transacción
        return validateClienteInput(cliente)
                .flatMap(valid -> validatePrivilegedFields(cliente))
                .flatMap(isAuthorized -> {
                    if (!isAuthorized) {
                        return Mono.error(new SecurityException("No tiene permisos para establecer campos administrativos (tieneCredito=true, esMayorista=true, esProveedor=true, estado=false)"));
                    }

                    // OPERACIÓN TRANSACCIONAL: Creación del cliente dentro de la transacción
                    return prepareClienteForCreation(cliente)
                            .flatMap(preparedCliente -> clienteRepository.save(preparedCliente))
                            .as(transactionalOperator::transactional);
                })
                .flatMap(savedCliente -> emitCliente(savedCliente.getId()).thenReturn(savedCliente))
                .doOnSuccess(savedCliente -> log.info("Cliente creado exitosamente con ID: {}", savedCliente.getId()))
                .onErrorResume(e -> {
                    log.error("Error al crear cliente: {}", e.getMessage());
                    return Mono.error(e);
                });
    }

    /**
     * Actualiza un cliente existente de manera optimizada.
     * Solo actualiza los campos que no son null en el objeto cliente.
     *
     * LÓGICA DE NEGOCIO:
     * 1. Valida que el ID del cliente sea requerido
     * 2. Verifica autorización para campos administrativos privilegiados
     * 3. Verifica que el cliente existe en la base de datos
     * 4. Actualiza solo los campos no null usando consulta optimizada
     * 5. Emite actualización a suscriptores vía RSocket
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Los campos tieneCredito=true, esMayorista=true, esProveedor=true y estado=false
     * requieren roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param clienteId ID del cliente a actualizar
     * @param cliente Cliente con los campos a actualizar (campos null se ignoran)
     * @return Mono que emite el Cliente actualizado
     */
    public Mono<Cliente> updateCliente(UUID clienteId, Cliente cliente) {
        log.info("Actualizando cliente con ID: {}", clienteId);

        // Validaciones básicas fuera de la transacción
        return validateUpdateInput(clienteId, cliente)
                .flatMap(valid -> validatePrivilegedFields(cliente))
                .flatMap(isAuthorized -> {
                    if (!isAuthorized) {
                        return Mono.error(new SecurityException("No tiene permisos para establecer campos administrativos (tieneCredito=true, esMayorista=true, esProveedor=true, estado=false)"));
                    }

                    return validateClienteExists(clienteId)
                            .flatMap(exists -> {
                                // OPERACIÓN TRANSACCIONAL: Actualización del cliente dentro de la transacción
                                return clienteRepository.updateClienteFields(
                                        clienteId,
                                        cliente.getNombreApellidos(),
                                        null,
                                        cliente.getRazonSocial(),
                                        cliente.getDni(),
                                        cliente.getRuc(),
                                        cliente.getOtroDocumento(),
                                        cliente.getDireccion(),
                                        cliente.getTelefono(),
                                        cliente.getEmail(),
                                        cliente.getTieneCredito(),
                                        cliente.getEsMayorista(),
                                        cliente.getEsProveedor(),
                                        cliente.getEstado(),
                                        cliente.getMetadata()
                                ).then(clienteRepository.findById(clienteId))
                                .as(transactionalOperator::transactional);
                            });
                })
                .flatMap(updatedCliente -> emitCliente(updatedCliente.getId()).thenReturn(updatedCliente))
                .doOnSuccess(updatedCliente -> log.info("Cliente actualizado exitosamente: {}", clienteId))
                .onErrorResume(e -> {
                    log.error("Error al actualizar cliente {}: {}", clienteId, e.getMessage());
                    return Mono.error(e);
                });
    }

    /**
     * Valida si el usuario actual tiene permisos para establecer campos privilegiados.
     * Los campos tieneCredito=true, esMayorista=true, esProveedor=true y estado=false
     * requieren roles ADMIN, MANAGER o ADMINISTRATOR.
     *
     * @param cliente Cliente con los campos a validar
     * @return Mono<Boolean> true si el usuario tiene permisos o no está intentando establecer campos privilegiados
     */
    private Mono<Boolean> validatePrivilegedFields(Cliente cliente) {
        // Verificar si se están intentando establecer campos privilegiados
        boolean hasPrivilegedFields = (cliente.getTieneCredito() != null && cliente.getTieneCredito()) ||
                                     (cliente.getEsMayorista() != null && cliente.getEsMayorista()) ||
                                     (cliente.getEsProveedor() != null && cliente.getEsProveedor()) ||
                                     (cliente.getEstado() != null && !cliente.getEstado());

        if (!hasPrivilegedFields) {
            // No se están estableciendo campos privilegiados, permitir la operación
            return Mono.just(true);
        }

        // Se están estableciendo campos privilegiados, verificar autorización
        return SecurityUtils.hasAnyRole(RoleEnum.ADMIN, RoleEnum.MANAGER, RoleEnum.ADMINISTRATOR)
                .doOnNext(hasRole -> {
                    if (hasRole) {
                        log.info("Usuario autorizado para establecer campos privilegiados de cliente");
                    } else {
                        log.warn("Usuario sin permisos intentó establecer campos privilegiados de cliente");
                    }
                });
    }

    // ==================== MÉTODOS AUXILIARES DE VALIDACIÓN ====================

    /**
     * Valida la entrada para la creación de un cliente según su tipo.
     * Reglas:
     * - NATURAL: requiere nombreApellidos y dni
     * - JURIDICO: requiere razonSocial y ruc
     */
    private Mono<Boolean> validateClienteInput(Cliente cliente) {
        if (cliente == null) {
            return Mono.error(new IllegalArgumentException("El cuerpo del cliente es requerido"));
        }
        if (cliente.getTipoCliente() == null) {
            return Mono.error(new IllegalArgumentException("El tipoCliente es requerido (NATURAL o JURIDICO)"));
        }

        switch (cliente.getTipoCliente()) {
            case NATURAL:
                if (cliente.getNombreApellidos() == null || cliente.getNombreApellidos().trim().isEmpty()) {
                    return Mono.error(new IllegalArgumentException("El nombreApellidos es requerido para cliente NATURAL"));
                }
                if (cliente.getDni() == null || cliente.getDni().trim().isEmpty()) {
                    return Mono.error(new IllegalArgumentException("El dni es requerido para cliente NATURAL"));
                }
                break;
            case JURIDICO:
                if (cliente.getRazonSocial() == null || cliente.getRazonSocial().trim().isEmpty()) {
                    return Mono.error(new IllegalArgumentException("La razonSocial es requerida para cliente JURIDICO"));
                }
                if (cliente.getRuc() == null || cliente.getRuc().trim().isEmpty()) {
                    return Mono.error(new IllegalArgumentException("El ruc es requerido para cliente JURIDICO"));
                }
                break;
            default:
                return Mono.error(new IllegalArgumentException("tipoCliente no soportado"));
        }

        return Mono.just(true);
    }

    /**
     * Valida la entrada para la actualización de un cliente.
     * Reglas mínimas:
     * - ID es requerido
     * - Si se envía tipoCliente, deben enviarse también sus campos obligatorios:
     *   NATURAL => nombreApellidos y dni
     *   JURIDICO => razonSocial y ruc
     */
    private Mono<Boolean> validateUpdateInput(UUID clienteId, Cliente cliente) {
        if (clienteId == null) {
            return Mono.error(new IllegalArgumentException("El ID del cliente es requerido"));
        }
        if (cliente != null && cliente.getTipoCliente() != null) {
            switch (cliente.getTipoCliente()) {
                case NATURAL:
                    if (cliente.getNombreApellidos() == null || cliente.getNombreApellidos().trim().isEmpty()) {
                        return Mono.error(new IllegalArgumentException("Debe enviar nombreApellidos al cambiar a NATURAL"));
                    }
                    if (cliente.getDni() == null || cliente.getDni().trim().isEmpty()) {
                        return Mono.error(new IllegalArgumentException("Debe enviar dni al cambiar a NATURAL"));
                    }
                    break;
                case JURIDICO:
                    if (cliente.getRazonSocial() == null || cliente.getRazonSocial().trim().isEmpty()) {
                        return Mono.error(new IllegalArgumentException("Debe enviar razonSocial al cambiar a JURIDICO"));
                    }
                    if (cliente.getRuc() == null || cliente.getRuc().trim().isEmpty()) {
                        return Mono.error(new IllegalArgumentException("Debe enviar ruc al cambiar a JURIDICO"));
                    }
                    break;
                default:
                    return Mono.error(new IllegalArgumentException("tipoCliente no soportado"));
            }
        }
        return Mono.just(true);
    }

    /**
     * Valida que el cliente existe en la base de datos.
     */
    private Mono<Boolean> validateClienteExists(UUID clienteId) {
        return clienteRepository.existsById(clienteId)
                .flatMap(exists -> {
                    if (!exists) {
                        return Mono.error(new IllegalArgumentException("Cliente no encontrado con ID: " + clienteId));
                    }
                    return Mono.just(true);
                });
    }

    /**
     * Prepara un cliente para la creación estableciendo valores por defecto.
     * Asegura que el ID sea null y establece valores por defecto para campos booleanos.
     */
    private Mono<Cliente> prepareClienteForCreation(Cliente cliente) {
        // Asegurar que el ID sea null para que se genere automáticamente
        cliente.setId(null);

        // Establecer valores por defecto si no están definidos
        if (cliente.getTieneCredito() == null) {
            cliente.setTieneCredito(false);
        }
        if (cliente.getEsMayorista() == null) {
            cliente.setEsMayorista(false);
        }
        if (cliente.getEsProveedor() == null) {
            cliente.setEsProveedor(false);
        }
        if (cliente.getEstado() == null) {
            cliente.setEstado(true);
        }

        return Mono.just(cliente);
    }

    /**
     * Emite el estado actual del Cliente con todas sus relaciones.
     * Método auxiliar para notificar actualizaciones a los suscriptores vía RSocket.
     *
     * @param clienteId ID del Cliente a emitir
     * @return Mono<Void> que completa cuando la operación termina
     */
    private Mono<Void> emitCliente(UUID clienteId) {
        return clienteRepository.findById(clienteId)
                .doOnNext(freshCliente -> {
                    log.debug("Emitiendo actualización para Cliente con ID: {}", clienteId);
                    updateSink.tryEmitNext(freshCliente);
                })
                .then();
    }
}
