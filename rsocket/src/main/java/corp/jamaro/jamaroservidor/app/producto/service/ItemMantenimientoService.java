package corp.jamaro.jamaroservidor.app.producto.service;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.producto.repository.ItemRepository;
import corp.jamaro.jamaroservidor.app.util.AtributoUtil;
import corp.jamaro.jamaroservidor.app.util.ItemUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Mono;

/**
 * Servicio para el mantenimiento de Items.
 * Proporciona operaciones CRUD para Items con validación y procesamiento automático del codCompuesto.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ItemMantenimientoService {
    private final TransactionalOperator transactionalOperator;
    private final ItemRepository itemRepository;
    private final ItemUtil itemUtil;
    private final AtributoUtil atributoUtil;

    /**
     * Valida que el Item tenga los datos requeridos para generar el codCompuesto.
     * 
     * @param item El Item a validar
     * @throws IllegalArgumentException si el Item no tiene los datos requeridos
     */
    private void validarDatosRequeridos(Item item) {
        itemUtil.validarItem(item);
        log.debug("Validación de datos requeridos completada para el Item");
    }

    /**
     * Valida que el Item tenga datos de negocio válidos.
     * 
     * @param item El Item a validar
     * @throws IllegalArgumentException si el Item tiene datos inválidos
     */
    private void validarDatosDeNegocio(Item item) {
        // Validar precios
        if (item.getPrecioCostoPromedio() != null && item.getPrecioCostoPromedio() < 0) {
            throw new IllegalArgumentException("El precio costo promedio no puede ser negativo");
        }
        if (item.getPrecioVentaBase() != null && item.getPrecioVentaBase() < 0) {
            throw new IllegalArgumentException("El precio venta base no puede ser negativo");
        }
        if (item.getPrecioVentaPublico() != null && item.getPrecioVentaPublico() < 0) {
            throw new IllegalArgumentException("El precio venta público no puede ser negativo");
        }
        
        // Validar stocks
        if (item.getStockTotal() != null && item.getStockTotal() < 0) {
            throw new IllegalArgumentException("El stock total no puede ser negativo");
        }
        if (item.getStockDeSeguridad() != null && item.getStockDeSeguridad() < 0) {
            throw new IllegalArgumentException("El stock de seguridad no puede ser negativo");
        }
        
        // Validar lógica de precios
        if (item.getPrecioVentaBase() != null && item.getPrecioVentaPublico() != null 
            && item.getPrecioVentaBase() > item.getPrecioVentaPublico()) {
            throw new IllegalArgumentException("El precio venta base no puede ser mayor al precio venta público");
        }
        
        log.debug("Validación de datos de negocio completada para el Item");
    }

    /**
     * Busca un Item por su codCompuesto.
     *
     * @param codCompuesto Código compuesto del Item.
     * @return Un Mono con el Item encontrado o vacío si no existe.
     */
    public Mono<Item> buscarItemPorCodCompuesto(String codCompuesto) {
        log.info("Buscando Item por codCompuesto: {}", codCompuesto);
        return itemRepository.findByCodCompuesto(codCompuesto.toLowerCase());
    }

    /**
     * Verifica si existe un Item con el codCompuesto especificado.
     *
     * @param codCompuesto Código compuesto del Item a verificar.
     * @return Un Mono con true si existe, false en caso contrario.
     */
    public Mono<Boolean> existeItemPorCodCompuesto(String codCompuesto) {
        log.info("Verificando existencia de Item por codCompuesto: {}", codCompuesto);
        return itemRepository.existsByCodCompuesto(codCompuesto);
    }

    /**
     * Crea un nuevo Item. Genera automáticamente el codCompuesto usando la lógica establecida
     * (Producto.codProductoOld + Marca.abreviacion). Procesa los atributos del item para generar sus IDs.
     * Valida que el codCompuesto generado no exista previamente.
     *
     * @param item El Item a crear.
     * @return Un Mono con el Item creado o error si ya existe.
     */
    public Mono<Item> crearItem(Item item) {
        log.info("Creando nuevo Item");

        // Validar datos requeridos
        validarDatosRequeridos(item);
        
        // Validar datos de negocio
        validarDatosDeNegocio(item);

        // Procesar atributos para generar sus IDs
        atributoUtil.procesarAtributos(item.getAtributos());

        // Generar codCompuesto
        itemUtil.procesarItem(item);
        
        String codCompuesto = item.getCodCompuesto();
        log.info("CodCompuesto generado para nuevo Item: {}", codCompuesto);

        // Verificar que no exista un item con el mismo codCompuesto
        return itemRepository.existsByCodCompuesto(codCompuesto)
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new IllegalArgumentException("Ya existe un Item con el codCompuesto: " + codCompuesto));
                    }
                    return itemRepository.save(item);
                })
                .flatMap(itemCreado -> {
                    // Limpiar atributos huérfanos después de crear el item
                    return atributoUtil.limpiarAtributosHuerfanos()
                            .doOnNext(count -> log.debug("Limpieza de atributos huérfanos completada después de crear item. Eliminados: {}", count))
                            .then(Mono.just(itemCreado));
                })
                .doOnSuccess(itemCreado -> log.info("Item creado exitosamente con codCompuesto: {}", itemCreado.getCodCompuesto()))
                .doOnError(error -> log.error("Error al crear Item", error));
    }

    /**
     * Actualiza un Item existente. Valida que exista el Item con el codCompuesto especificado.
     * Procesa los atributos del item para generar sus IDs según la lógica establecida.
     * Usa transaccionalidad para garantizar la consistencia de los datos.
     * 
     * IMPORTANTE: El codCompuesto NO se puede cambiar en una actualización, ya que es el ID del Item.
     * Si se necesita cambiar el Producto o la Marca, se debe eliminar el Item y crear uno nuevo.
     *
     * @param itemActualizado El Item con los datos actualizados.
     * @return Un Mono con el Item actualizado.
     */
    public Mono<Item> actualizarItem(Item itemActualizado) {
        if (itemActualizado.getCodCompuesto() == null || itemActualizado.getCodCompuesto().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("El codCompuesto es requerido para actualizar un Item"));
        }

        String codCompuesto = itemActualizado.getCodCompuesto();
        log.info("Actualizando Item con codCompuesto: {}", codCompuesto);

        // Procesar atributos para generar sus IDs
        atributoUtil.procesarAtributos(itemActualizado.getAtributos());

        // Validar datos de negocio (no validamos datos requeridos porque el codCompuesto ya existe)
        validarDatosDeNegocio(itemActualizado);

        return transactionalOperator.transactional(
                itemRepository.findByCodCompuesto(codCompuesto)
                        .switchIfEmpty(Mono.error(new IllegalArgumentException("No existe un Item con el codCompuesto: " + codCompuesto)))
                        .flatMap(itemExistente -> {
                            // Verificar que el codCompuesto no haya cambiado
                            if (!codCompuesto.equals(itemActualizado.getCodCompuesto())) {
                                return Mono.error(new IllegalArgumentException("No se puede cambiar el codCompuesto de un Item existente"));
                            }
                            
                            // Actualizar el item directamente
                            // Las relaciones se actualizan automáticamente por Spring Data Neo4j
                            return itemRepository.save(itemActualizado);
                        })
                        .flatMap(itemActualizadoGuardado -> {
                            // Limpiar atributos huérfanos después de actualizar el item
                            return atributoUtil.limpiarAtributosHuerfanos()
                                    .doOnNext(count -> log.debug("Limpieza de atributos huérfanos completada después de actualizar item. Eliminados: {}", count))
                                    .then(Mono.just(itemActualizadoGuardado));
                        })
        )
        .doOnSuccess(itemActualizado1 -> log.info("Item actualizado exitosamente con codCompuesto: {}", itemActualizado1.getCodCompuesto()))
        .doOnError(error -> log.error("Error al actualizar Item con codCompuesto: {}", codCompuesto, error));
    }

}