package corp.jamaro.jamaroservidor.app.producto.service;

import corp.jamaro.jamaroservidor.app.producto.model.Grupo;
import corp.jamaro.jamaroservidor.app.producto.model.NombreGrupo;
import corp.jamaro.jamaroservidor.app.producto.model.enums.TipoGrupo;
import corp.jamaro.jamaroservidor.app.producto.repository.FiltroRepository;
import corp.jamaro.jamaroservidor.app.producto.repository.GrupoRepository;
import corp.jamaro.jamaroservidor.app.producto.repository.NombreGrupoRepository;
import corp.jamaro.jamaroservidor.app.util.RegexUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class GrupoCategoriaService {
    private final TransactionalOperator transactionalOperator;
    private final GrupoRepository grupoRepository;
    private final NombreGrupoRepository nombreGrupoRepository;
    private final FiltroRepository filtroRepository;

    /**
     * Busca los NombreGrupo cuyo campo nombre coincida con una expresión regular dada 
     * y que tengan relación con los nodos Grupo cuyo tipo es categoria.
     * Se utiliza RegexUtil para escapar correctamente los caracteres especiales.
     *
     * @param userInput Texto ingresado por el usuario para construir la expresión regular.
     * @return Un Flux que emite los NombreGrupo que cumplen el criterio.
     */
    public Flux<NombreGrupo> buscarNombresGrupoPorRegex(String userInput) {
        log.info("Buscando NombreGrupo por regex para grupos de tipo categoria: {}", userInput);
        String regex = RegexUtil.buildContainsAllRegex(userInput);
        return nombreGrupoRepository.findNombresGrupoByNombreAndGrupoCategoria(regex)
                .doOnNext(nombreGrupo -> log.info("NombreGrupo encontrado y devuelto: {}", nombreGrupo.getNombre()));
    }

    /**
     * Recibe el id de un NombreGrupo y obtiene el Grupo con el que tiene relación.
     * Primero hace una consulta cypher simple en el repository y luego un findById para obtener el objeto completo.
     *
     * @param nombreGrupoId UUID del NombreGrupo.
     * @return Un Mono con el Grupo encontrado o vacío si no existe.
     */
    public Mono<Grupo> obtenerGrupoPorNombreGrupoId(UUID nombreGrupoId) {
        log.info("Obteniendo Grupo por NombreGrupo id: {}", nombreGrupoId);

        return grupoRepository.findGrupoByNombreGrupoId(nombreGrupoId)
                .flatMap(grupo -> grupoRepository.findById(grupo.getId()));
    }

    /**
     * Crea un nuevo Grupo del tipo categoria. Recibe un Grupo que el cliente ya construyó 
     * con un id asignado. Comprueba que este aún no exista, si no existe y el tipo es categoria 
     * procede a hacer un save.
     *
     * @param grupo El Grupo a crear.
     * @return Un Mono con el Grupo creado o error si ya existe o no es del tipo categoria.
     */
    public Mono<Grupo> crearGrupoCategoria(Grupo grupo) {
        log.info("Creando nuevo Grupo categoria con id: {}", grupo.getId());

        // Validar que el tipo sea categoria
        if (!TipoGrupo.categoria.equals(grupo.getTipo())) {
            return Mono.error(new IllegalArgumentException("El Grupo debe ser del tipo categoria"));
        }

        // Verificar que no exista
        return grupoRepository.existsById(grupo.getId())
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new IllegalArgumentException("Ya existe un Grupo con el id: " + grupo.getId()));
                    }
                    return grupoRepository.save(grupo);
                })
                .flatMap(grupoCreado -> limpiarNodosHuerfanos().thenReturn(grupoCreado));
    }

    /**
     * Actualiza un Grupo existente del tipo categoria. Valida que exista y que sea del tipo categoria.
     * Usa transaccionalidad y varios métodos internos para actualizar de manera eficiente:
     * - Compara los ids de los nombresGrupo del antiguo con el nuevo y elimina los que ya no existan
     * - Compara los ids de los filtros y elimina las relaciones de los que ya no existan
     * - Los nodos Grupo del tipo categoria no usan el campo subGrupos
     * - Luego hace el save
     *
     * @param grupoActualizado El Grupo con los datos actualizados.
     * @return Un Mono con el Grupo actualizado.
     */
    public Mono<Grupo> actualizarGrupoCategoria(Grupo grupoActualizado) {
        log.info("Actualizando Grupo categoria con id: {}", grupoActualizado.getId());

        // Validar que el tipo sea categoria
        if (!TipoGrupo.categoria.equals(grupoActualizado.getTipo())) {
            return Mono.error(new IllegalArgumentException("El Grupo debe ser del tipo categoria"));
        }

        return transactionalOperator.transactional(
                grupoRepository.findById(grupoActualizado.getId())
                        .switchIfEmpty(Mono.error(new IllegalArgumentException("No existe un Grupo con el id: " + grupoActualizado.getId())))
                        .flatMap(grupoExistente -> {
                            // Validar que el grupo existente sea del tipo categoria
                            if (!TipoGrupo.categoria.equals(grupoExistente.getTipo())) {
                                return Mono.error(new IllegalArgumentException("El Grupo existente debe ser del tipo categoria"));
                            }

                            return limpiarRelacionesObsoletas(grupoExistente, grupoActualizado)
                                    .then(grupoRepository.save(grupoActualizado))
                                    .flatMap(grupoGuardado -> limpiarNodosHuerfanos().thenReturn(grupoGuardado));
                        })
        );
    }

    /**
     * Método interno para limpiar las relaciones obsoletas antes de actualizar.
     */
    private Mono<Void> limpiarRelacionesObsoletas(Grupo grupoExistente, Grupo grupoActualizado) {
        return Mono.fromRunnable(() -> {
            // Limpiar subGrupos ya que los nodos Grupo del tipo categoria no los usan
            grupoActualizado.setSubGrupos(null);
        })
        .then(limpiarNombresGrupoObsoletos(grupoExistente, grupoActualizado))
        .then(limpiarFiltrosObsoletos(grupoExistente, grupoActualizado));
    }

    /**
     * Compara los ids de los nombresGrupo del antiguo con el nuevo Grupo y elimina completamente 
     * los nodos NombreGrupo que ya no existan (ya que quedarían huérfanos).
     */
    private Mono<Void> limpiarNombresGrupoObsoletos(Grupo grupoExistente, Grupo grupoActualizado) {
        Set<UUID> idsExistentes = grupoExistente.getNombresGrupo() != null ? 
                grupoExistente.getNombresGrupo().stream()
                        .map(NombreGrupo::getId)
                        .collect(Collectors.toSet()) : Set.of();

        Set<UUID> idsNuevos = grupoActualizado.getNombresGrupo() != null ? 
                grupoActualizado.getNombresGrupo().stream()
                        .map(NombreGrupo::getId)
                        .collect(Collectors.toSet()) : Set.of();

        List<UUID> idsAEliminar = idsExistentes.stream()
                .filter(id -> !idsNuevos.contains(id))
                .collect(Collectors.toList());

        if (idsAEliminar.isEmpty()) {
            return Mono.empty();
        }

        log.info("Eliminando nodos NombreGrupo obsoletos (quedarían huérfanos): {}", idsAEliminar);
        return grupoRepository.deleteNombreGrupoRelations(grupoExistente.getId(), idsAEliminar);
    }

    /**
     * Compara los ids de los filtros y elimina las relaciones de los que ya no existan.
     */
    private Mono<Void> limpiarFiltrosObsoletos(Grupo grupoExistente, Grupo grupoActualizado) {
        Set<UUID> idsExistentes = grupoExistente.getFiltros() != null ? 
                grupoExistente.getFiltros().stream()
                        .map(relacion -> relacion.getFiltro().getId())
                        .collect(Collectors.toSet()) : Set.of();

        Set<UUID> idsNuevos = grupoActualizado.getFiltros() != null ? 
                grupoActualizado.getFiltros().stream()
                        .map(relacion -> relacion.getFiltro().getId())
                        .collect(Collectors.toSet()) : Set.of();

        List<UUID> idsAEliminar = idsExistentes.stream()
                .filter(id -> !idsNuevos.contains(id))
                .collect(Collectors.toList());

        if (idsAEliminar.isEmpty()) {
            return Mono.empty();
        }

        log.info("Eliminando relaciones con Filtros obsoletos: {}", idsAEliminar);
        return grupoRepository.deleteFiltroRelations(grupoExistente.getId(), idsAEliminar);
    }

    /**
     * Método interno que elimina los nodos huérfanos después de las operaciones de creación o actualización.
     * Elimina:
     * - Nodos NombreGrupo que no tienen relación con ningún Grupo
     * - Nodos Filtro que no tienen relación con ningún Grupo y con ningún Atributo
     */
    private Mono<Void> limpiarNodosHuerfanos() {
        log.info("Iniciando limpieza de nodos huérfanos");

        return nombreGrupoRepository.deleteOrphanedNombreGrupos()
                .doOnSuccess(unused -> log.info("Nodos NombreGrupo huérfanos eliminados"))
                .then(filtroRepository.deleteOrphanedFiltros())
                .doOnSuccess(unused -> log.info("Nodos Filtro huérfanos eliminados"))
                .doOnSuccess(unused -> log.info("Limpieza de nodos huérfanos completada"));
    }
}
