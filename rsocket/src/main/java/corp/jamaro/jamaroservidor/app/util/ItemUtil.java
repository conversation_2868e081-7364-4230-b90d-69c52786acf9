package corp.jamaro.jamaroservidor.app.util;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.producto.repository.ItemRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.Set;

/**
 * Utilidad para el manejo de Items.
 * Proporciona métodos reutilizables para la generación de codCompuesto y procesamiento de Items.
 */
@Slf4j
@Component
public class ItemUtil {

    private final ItemRepository itemRepository;

    @Autowired
    public ItemUtil(ItemRepository itemRepository) {
        this.itemRepository = itemRepository;
    }

    /**
     * Genera el codCompuesto de un Item siguiendo la lógica: Producto.codProductoOld + Marca.abreviacion
     * 
     * @param item El Item para el cual generar el codCompuesto
     * @return El codCompuesto generado para el Item
     * @throws IllegalArgumentException si el Item, su Producto, Marca o sus campos requeridos son nulos
     */
    public String generarCodCompuesto(Item item) {
        // Reutilizar la validación existente para aplicar DRY
        validarItem(item);
        
        String codProductoOld = item.getProducto().getCodProductoOld().trim().toLowerCase();
        String marcaAbreviacion = item.getMarca().getAbreviacion().trim().toLowerCase();
        String codCompuesto = codProductoOld + marcaAbreviacion;
        
        log.debug("CodCompuesto generado: {} para producto: {} y marca: {}", 
                 codCompuesto, codProductoOld, marcaAbreviacion);
        
        return codCompuesto;
    }

    /**
     * Filtra y elimina items con datos requeridos nulos o vacíos del conjunto.
     * Esto previene errores durante el procesamiento.
     * 
     * @param items El conjunto de items a filtrar
     * @return El número de items eliminados
     */
    private int filtrarItemsInvalidos(Set<Item> items) {
        if (items == null || items.isEmpty()) {
            return 0;
        }
        
        int itemsEliminados = 0;
        Iterator<Item> iterator = items.iterator();
        
        while (iterator.hasNext()) {
            Item item = iterator.next();
            
            // Verificar si el item tiene los datos requeridos para generar codCompuesto
            boolean esInvalido = false;
            String razonEliminacion = "";
            
            if (item.getProducto() == null) {
                esInvalido = true;
                razonEliminacion = "Producto nulo";
            } else if (item.getProducto().getCodProductoOld() == null || item.getProducto().getCodProductoOld().trim().isEmpty()) {
                esInvalido = true;
                razonEliminacion = "codProductoOld nulo o vacío";
            } else if (item.getMarca() == null) {
                esInvalido = true;
                razonEliminacion = "Marca nula";
            } else if (item.getMarca().getAbreviacion() == null || item.getMarca().getAbreviacion().trim().isEmpty()) {
                esInvalido = true;
                razonEliminacion = "abreviacion de Marca nula o vacía";
            }
            
            if (esInvalido) {
                log.info("Eliminando item inválido. Razón: {}", razonEliminacion);
                iterator.remove();
                itemsEliminados++;
            }
        }
        
        if (itemsEliminados > 0) {
            log.info("Se eliminaron {} items inválidos", itemsEliminados);
        }
        
        return itemsEliminados;
    }

    /**
     * Procesa un conjunto de items, filtrando primero los items con datos requeridos nulos o vacíos,
     * luego generando sus codCompuesto según la lógica establecida.
     * 
     * @param items El conjunto de items a procesar
     */
    public void procesarItems(Set<Item> items) {
        if (items != null) {
            // Primero filtrar items con datos nulos o vacíos para evitar problemas
            filtrarItemsInvalidos(items);
            
            // Procesar solo los items válidos restantes
            for (Item item : items) {
                String nuevoCodCompuesto = generarCodCompuesto(item);
                item.setCodCompuesto(nuevoCodCompuesto);
                log.debug("CodCompuesto asignado: {} al item", nuevoCodCompuesto);
            }
        }
    }

    /**
     * Procesa un item individual, generando su codCompuesto según la lógica establecida.
     * 
     * @param item El item a procesar
     */
    public void procesarItem(Item item) {
        if (item != null) {
            String nuevoCodCompuesto = generarCodCompuesto(item);
            item.setCodCompuesto(nuevoCodCompuesto);
            log.debug("CodCompuesto asignado: {} al item", nuevoCodCompuesto);
        }
    }

    /**
     * Valida que un item tenga todos los datos requeridos para ser procesado.
     * 
     * @param item El item a validar
     * @throws IllegalArgumentException si el item no tiene los datos requeridos
     */
    public void validarItem(Item item) {
        if (item == null) {
            throw new IllegalArgumentException("El Item no puede ser nulo");
        }
        if (item.getProducto() == null) {
            throw new IllegalArgumentException("El Item debe tener un Producto válido");
        }
        if (item.getProducto().getCodProductoOld() == null || item.getProducto().getCodProductoOld().trim().isEmpty()) {
            throw new IllegalArgumentException("El Producto del Item debe tener un codProductoOld válido");
        }
        if (item.getMarca() == null) {
            throw new IllegalArgumentException("El Item debe tener una Marca válida");
        }
        if (item.getMarca().getAbreviacion() == null || item.getMarca().getAbreviacion().trim().isEmpty()) {
            throw new IllegalArgumentException("La Marca del Item debe tener una abreviacion válida");
        }
        
        log.debug("Item validado correctamente");
    }
}