plugins {
    id 'java' // Should this be java-library too? For now, keeping as java.
    id 'io.spring.dependency-management' version '1.1.6'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.boot:spring-boot-dependencies:3.4.0"
    }
}

group = 'corp.jamaro'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-rsocket'
    implementation 'org.springframework.boot:spring-boot-starter-validation' // Likely needed by controllers/services

    // Dependencies on other local modules
    implementation project(':data-neo4j')
    implementation project(':security') // For SecurityUtils, @PreAuthorize etc.
    implementation project(':minio') // For MinioService, MinioException
    implementation project(':sunat-services') // Para usar SunatInvoiceService desde RSocket

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor' // If @ConfigurationProperties are used here

    // Testing dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.mockito:mockito-core'
    testImplementation 'org.mockito:mockito-junit-jupiter'

    // Add other dependencies previously in the main build.gradle that are ONLY used by rsocket components
}

test {
    useJUnitPlatform()
}
