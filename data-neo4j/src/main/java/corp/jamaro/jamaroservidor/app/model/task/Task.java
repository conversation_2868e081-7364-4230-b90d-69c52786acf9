package corp.jamaro.jamaroservidor.app.model.task;

import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.model.enums.PriorityEnum;
import corp.jamaro.jamaroservidor.app.model.enums.TaskStatusEnum;
import corp.jamaro.jamaroservidor.app.model.enums.TipoTaskEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Representa una tarea genérica en el sistema.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@Node
public class Task {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String title;
    private String descripcion;
    private Instant creadoActualizado = Instant.now();
    private Instant dueDate;

    private TaskStatusEnum estado = TaskStatusEnum.PENDIENTE;
    private PriorityEnum prioridad = PriorityEnum.MEDIA;
    private float progress = 0.0f;

    /**
     * Campo adicional para indicar el tipo de tarea (genérico, subir archivo, etc.).
     */
    private TipoTaskEnum tipo = TipoTaskEnum.GENERICO;

    /**
     * Indica el esfuerzo, dificultad o peso de la tarea (puede ser 1-10, o un double arbitrario).
     */
    private double peso = 1.0;

    @Relationship(type = "ASIGNADO_AL_USUARIO")
    private List<User> assignedUsers;

    @Relationship(type = "CREADO_POR")
    private User assignedBy;

    /**
     * Permite modelar sub-tareas, que pueden ser también hijas de Task
     * (por ejemplo, UploadFileTask o cualquier subclase).
     */
    @Relationship(type = "HAS_SUB_TASK")
    private List<Task> subTasks;

    /**
     * Constructor completo.
     */
    public Task(UUID id,
                String title,
                String descripcion,
                Instant creadoActualizado,
                Instant dueDate,
                TaskStatusEnum estado,
                PriorityEnum prioridad,
                float progress,
                TipoTaskEnum tipo,
                double peso,
                List<User> assignedUsers,
                User assignedBy,
                List<Task> subTasks) {
        this.id = id;
        this.title = title;
        this.descripcion = descripcion;
        this.creadoActualizado = (creadoActualizado != null) ? creadoActualizado : Instant.now();
        this.dueDate = dueDate;
        this.estado = (estado != null) ? estado : TaskStatusEnum.PENDIENTE;
        this.prioridad = (prioridad != null) ? prioridad : PriorityEnum.MEDIA;
        this.progress = progress;
        this.tipo = (tipo != null) ? tipo : TipoTaskEnum.GENERICO;
        this.peso = peso;
        this.assignedUsers = assignedUsers;
        this.assignedBy = assignedBy;
        this.subTasks = subTasks;
    }
}
