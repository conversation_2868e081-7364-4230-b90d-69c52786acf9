package corp.jamaro.jamaroservidor.app.ventas.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;
import java.util.UUID;

/**
 * DTO para exponer únicamente la información esencial de UniversalSaleGui.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UniversalSaleGuiDto {
    private UUID id;               // ID de la UniversalSaleGui
    private String userUsername;   // username del User
    private UUID mainSaleGuiId;    // ID del MainSaleGui
    private Set<UUID> auditingMainSales;
    private String guiConfig;
}
