package corp.jamaro.jamaroservidor.app.producto.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;


@Data
@Node
public class Atributo {
    @Id
    private String id;

    @Relationship(type = "PERTENECE_AL_FILTRO")
    private Filtro filtro;

    private String dato; // Campo unificado que reemplaza datoString, datoNumerico, datoDicotomico y datoCompuesto

//    @Relationship(type = "CON_ATRIBUTO_DATO_FILE")
//    private Set<ToBucketFileRelation> datoFile;// Generalmente van a ser Imagenes, pero pueden ser animaciones, modelos 3d etc.
}
