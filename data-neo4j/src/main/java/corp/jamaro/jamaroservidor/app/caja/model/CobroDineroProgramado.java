package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.dinero.model.DineroEntrada;
import corp.jamaro.jamaroservidor.app.dinero.model.DineroSalida;
import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class CobroDineroProgramado {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private Double montoACobrar;//el monto en Soles ya que el sistema trabajar por defecto en soles.
    private Double montoRestante;//el monto restante a cobrar, se actualiza cada vez que se cobra dinero.

    @Relationship(type = "CON_DINERO_COBRADO")//aquí podemos ver quien o quienes cobraron el dinero, ya que dinero tiene campo user
    private Set<DineroEntrada> cobros;//pueden ser varios ya que hay varios metodos de pago y puede ser pago mixto, y en credito pueden ser varios pagos en un inicio nisiquiera va a existir hasta que se cobre, se programará un cobro y cuando se cobra recien se crea el dinero con esEntrada true.

    @Relationship(type = "CON_VUELTO_DEL_COBRO")
    private Set<DineroSalida> vueltos;//los vueltos que se dan pueden ser Efectivo o Digital

    @Relationship(type = "INICIADO_POR")
    private User iniciadoPor;//usuario que inicia el cobro en un Sale o alguna otra entrada de dinero

    private Instant creadoEl = Instant.now();
    private Instant fechaLimite;
    private Instant terminadoDeCobrarEl;

    private Boolean estaCobrado = false;// true (cobrado por completo) false (no cobrado por completo) para encontrar facilmente que dinero faltan cobrar en caja.

}
