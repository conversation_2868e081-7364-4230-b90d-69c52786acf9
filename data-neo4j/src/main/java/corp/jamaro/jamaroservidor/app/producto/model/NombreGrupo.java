package corp.jamaro.jamaroservidor.app.producto.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.Instant;
import java.util.UUID;

@Node
@Data
public class NombreGrupo {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String nombre; // direccion, kit de embrague, retenes, rodaje, rodaje de rueda, reten de rueda, etc //y es UNIQUE
    private Boolean isPrincipal;// para saber si es el nombre principal o apodos
    private Instant createdAt= Instant.now(); // nos ayudará a solucionar problemas en caso de dos isPrincipal en True para el mismo Grupo
}
