package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.Atributo;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import reactor.core.publisher.Mono;

public interface AtributoRepository extends ReactiveNeo4jRepository<Atributo, String> {

    /**
     * Elimina todos los nodos Atributo que solo tienen relación con Filtro
     * y no tienen relación con ningún otro nodo.
     * Usa DETACH DELETE para eliminar automáticamente todas las relaciones antes de eliminar el nodo.
     * 
     * @return Mono<Integer> con el número de nodos Atributo eliminados
     */
    @Query("MATCH (a:Atributo)-[:PERTENECE_AL_FILTRO]->(f:Filtro) " +
           "WHERE NOT (a)<-[:PRODUCTO_CON_ATRIBUTO]-(:Producto) " +
           "AND NOT (a)<-[:CON_ATRIBUTO]-(:Item) " +
           "DETACH DELETE a " +
           "RETURN count(a)")
    Mono<Integer> eliminarAtributosSoloConFiltro();

    /**
     * Elimina todos los nodos Atributo que no tienen relación con Filtro.
     * Usa DETACH DELETE para eliminar automáticamente todas las relaciones antes de eliminar el nodo.
     * 
     * @return Mono<Integer> con el número de nodos Atributo eliminados
     */
    @Query("MATCH (a:Atributo) " +
           "WHERE NOT (a)-[:PERTENECE_AL_FILTRO]->(:Filtro) " +
           "DETACH DELETE a " +
           "RETURN count(a)")
    Mono<Integer> eliminarAtributosSinFiltro();

    /**
     * Elimina todos los nodos Atributo que son completamente huérfanos (sin relaciones).
     * Usa DETACH DELETE para consistencia, aunque estos nodos no deberían tener relaciones.
     * 
     * @return Mono<Integer> con el número de nodos Atributo eliminados
     */
    @Query("MATCH (a:Atributo) " +
           "WHERE NOT (a)--() " +
           "DETACH DELETE a " +
           "RETURN count(a)")
    Mono<Integer> eliminarAtributosCompletamenteHuerfanos();

    /**
     * Método combinado que ejecuta todas las limpiezas de atributos huérfanos.
     * Elimina:
     * 1. Atributos que solo tienen relación con Filtro y ninguna otra
     * 2. Atributos que no tienen relación con Filtro
     * 3. Atributos completamente huérfanos
     * 
     * @return Mono<Integer> con el número total de nodos Atributo eliminados
     */
    default Mono<Integer> eliminarAtributosHuerfanos() {
        return eliminarAtributosSoloConFiltro()
                .flatMap(count1 -> eliminarAtributosSinFiltro()
                        .flatMap(count2 -> eliminarAtributosCompletamenteHuerfanos()
                                .map(count3 -> count1 + count2 + count3)));
    }
}