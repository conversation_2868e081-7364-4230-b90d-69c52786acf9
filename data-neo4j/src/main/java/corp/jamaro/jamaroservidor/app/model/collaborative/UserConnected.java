package corp.jamaro.jamaroservidor.app.model.collaborative;

import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.RelationshipId;
import org.springframework.data.neo4j.core.schema.RelationshipProperties;
import org.springframework.data.neo4j.core.schema.TargetNode;

import java.time.Instant;
import java.util.UUID;

@Data
@RelationshipProperties
public class UserConnected {
    @RelationshipId
    @Id
    private String id;

    @TargetNode
    private User user;

    private Instant lastConnectionAt;
    private Instant lastDisconnectionAt;

    private String clientType;
    private String clientVersion;

    private Boolean isOnline;

    //Agregar aqui un campo de User Preferences que será un json que guardará cosas como Orden de presentacion en la GUI, Posiciones de Separadores X y Y etc. Pero por ahora solo el Orden de Presentacion que es un Entero
    // Campo para guardar las preferencias de GUI en formato JSON (por ejemplo, {"presentationOrder": 1})
    private String userGuiPreferences;

}
