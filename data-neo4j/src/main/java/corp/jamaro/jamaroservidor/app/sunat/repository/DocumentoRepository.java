package corp.jamaro.jamaroservidor.app.sunat.repository;

import corp.jamaro.jamaroservidor.app.sunat.Documento;
import org.springframework.data.neo4j.repository.Neo4jRepository;

import java.util.UUID;

/**
 * Repositorio para la entidad Documento.
 * Incluye utilidades para actualizar el estado de SUNAT de forma optimizada
 * y buscar por serieAndNumero.
 */
public interface DocumentoRepository extends Neo4jRepository<Documento, UUID> {

}
