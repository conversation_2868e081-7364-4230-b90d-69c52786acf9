package corp.jamaro.jamaroservidor.app.sunat;

import corp.jamaro.jamaroservidor.app.model.Cliente;
import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.sunat.enums.EstadoSunat;
import corp.jamaro.jamaroservidor.app.sunat.enums.TipoDocumento;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
@Data
@Node
public class Documento {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    /**
     * Serie y numero de la boleta (B001-00000001) o FACTURA (F001-00000001) o TICKET o NOTA DE CREDITO etc
     */
    private String serieAndNumero;//hacer este campo UNIQUE en la db este se genera con ayuda de DocumentCounter

    private TipoDocumento tipoDocumento;// BOLETA, FACTURA, GUIA_REMISION, NOTA_CREDITO, NOTA_DEBITO, PROFORMA

    //generado por el User
    @Relationship(type = "GENERADA_POR_USER")
    private User generadaPor;

    //relacion con un conjunto de ventas consolidadas
    @Relationship(type = "INVOICE_PERTENECE_A_VENTA")
    private SaleConsolidado ventasConsolidado;// normalmente, va a pertenecer solo a una, pero a veces puede pertenecer a varias

    @Relationship(type = "BOLETA_PERTENECE_A_CLIENTE")
    private Cliente cliente;

    private Instant fechaEmision = Instant.now();

    private BigDecimal totalImporte;//ya incluye el igv.

    private Map<String, String> datosExtra;// a veces nos piden anotar placa del carro.

    /**
     * Estado de la boleta frente a SUNAT (Ej. ACEPTADO, RECHAZADO, PENDIENTE, ANULADO, ERROR)
     */
    private EstadoSunat estadoSunat;


}
/*
Lógica de generación de numero
Cuando vayas a crear una boleta:

Buscar el DocumentCounter para tipo "BOLETA" y serie "B001".

Incrementar ultimoNumero en 1.

Guardar el DocumentCounter actualizado.

Crear la Documento con ese número.

Esto asegura que incluso con varios usuarios simultáneos, si la actualización del contador se hace en una transacción (o con un MATCH ... SET ultimoNumero = ultimoNumero + 1 RETURN ultimoNumero en Cypher), no se repitan números.
 */