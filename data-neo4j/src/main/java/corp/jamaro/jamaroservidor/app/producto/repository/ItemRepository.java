package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface ItemRepository extends ReactiveNeo4jRepository<Item, UUID> {

    // Obtener Items asociados a un Producto mediante la relación ITEM_PARTE_DEL_PRODUCTO con todas sus relaciones
    @Query("""
            MATCH (i:Item)-[:ITEM_PARTE_DEL_PRODUCTO]->(p:Producto {id: $productoId})
            OPTIONAL MATCH (i)-[rcf:CON_CODIGO_FABRICA]->(cf:CodigoFabrica)
            OPTIONAL MATCH (i)-[rm:CON_MARCA]->(m:Marca)
            OPTIONAL MATCH (i)-[ra:CON_ATRIBUTO]->(a:Atributo)
            OPTIONAL MATCH (a)-[rf:PERTENECE_AL_FILTRO]->(f:Filtro)
            OPTIONAL MATCH (i)-[ru:CON_CODIGO_FABRICA]->(u:Ubicacion)
            RETURN i, collect(rcf), collect(cf), collect(rm), collect(m),
                   collect(ra), collect(a), collect(rf), collect(f),
                   collect(ru), collect(u)
            """)
    Flux<Item> findByProductoId(@Param("productoId") UUID productoId);


    /**
     * Busca un Item por su codCompuesto.
     *
     * @param codCompuesto Código compuesto del Item a buscar
     * @return El Item encontrado o vacío si no existe
     */
    Mono<Item> findByCodCompuesto(String codCompuesto);

    /**
     * Verifica si existe un Item con el codCompuesto especificado.
     *
     * @param codCompuesto Código compuesto del Item a verificar
     * @return true si existe, false en caso contrario
     */
    @Query("MATCH (i:Item {codCompuesto: $codCompuesto}) RETURN COUNT(i) > 0")
    Mono<Boolean> existsByCodCompuesto(@Param("codCompuesto") String codCompuesto);

    /**
     * Actualiza el stockTotal de un Item sumando o restando la cantidad especificada.
     * Si cantidad es positiva, aumenta el stock. Si es negativa, lo disminuye.
     *
     * @param codCompuesto Código compuesto del Item a actualizar
     * @param cantidad Cantidad a sumar (positiva) o restar (negativa) del stockTotal
     * @return El Item actualizado
     */
    @Query("MATCH (i:Item {codCompuesto: $codCompuesto}) " +
           "SET i.stockTotal = i.stockTotal + $cantidad " +
           "RETURN i")
    Mono<Item> updateStockTotal(@Param("codCompuesto") String codCompuesto, @Param("cantidad") Double cantidad);

    /**
     * Busca Items que tienen relación con un Producto mediante codProductoOld.
     * Retorna una lista simple de Items sin todas sus relaciones.
     *
     * @param codProductoOld Código del producto en el sistema anterior
     * @return Flux de Items básicos relacionados con el producto
     */
    @Query("MATCH (i:Item)-[:ITEM_PARTE_DEL_PRODUCTO]->(p:Producto {codProductoOld: $codProductoOld}) RETURN i")
    Flux<Item> findByCodProductoOld(@Param("codProductoOld") String codProductoOld);
}
