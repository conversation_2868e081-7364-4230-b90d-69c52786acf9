package corp.jamaro.jamaroservidor.app.ventas.model.gui;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.Set;
import java.util.UUID;

@Data
@Node
public class MainSaleGui {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String usernameOwner; // el username del User al que le pertenece

    @Relationship(type = "CON_SALE_GUI")
    private Set<ToSaleGuiRelation> salesGui;

}
