package corp.jamaro.jamaroservidor.app.sunat.enums;

/**
 * Tipo de comprobante alineado a los códigos SUNAT del Catálogo 01
 * (01=Factura, 03=Boleta, 07=Nota de Crédito, 08=Nota de Débito, 09=Guía de Remisión).
 * Incluye utilidades para derivar el tipo desde la serie o desde el código.
 */
public enum TipoDocumento {
    FACTURA("01"),
    BOLETA("03"),
    NOTA_CREDITO("07"),
    NOTA_DEBITO("08"),
    GUIA_REMISION("09"),
    TICKET("00"); // No se envía a SUNAT, código interno

    private final String sunatCode;

    TipoDocumento(String sunatCode) {
        this.sunatCode = sunatCode;
    }

    public String getSunatCode() {
        return sunatCode;
    }


    /**
     * Mapea un código SUNAT (01,03,07,08,09) a TipoDocumento.
     */
    public static TipoDocumento fromSunatCode(String code) {
        if (code == null) return TICKET;
        for (TipoDocumento t : values()) {
            if (t.sunatCode.equals(code)) return t;
        }
        return TICKET;
    }
}
