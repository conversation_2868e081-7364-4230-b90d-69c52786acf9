package corp.jamaro.jamaroservidor.app.caja.model.gui;

import corp.jamaro.jamaroservidor.app.caja.model.CajaDineroDigital;
import corp.jamaro.jamaroservidor.app.caja.model.CajaDineroEfectivo;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class CajaGui {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String nombreCaja;

    @Relationship(type = "CON_CAJA_DINERO_EFECTIVO")
    private CajaDineroEfectivo cajaDineroEfectivo;

    @Relationship(type = "CON_CAJA_DINERO_DIGITAL")
    private CajaDineroDigital cajaDineroDigital;

    private String guiConfig; // un json con las preferencias del usuario para su gui (color de usuario, tamaña letra, posición del slider, etc.)

    private Instant createdAt = Instant.now();


}
