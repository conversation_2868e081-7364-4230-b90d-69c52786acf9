package corp.jamaro.jamaroservidor.app.ventas.model;

import corp.jamaro.jamaroservidor.app.producto.model.Item;
import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class BienServicioDevuelto {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "DEVUELTO_POR")
    private User devueltoPor; // usuario que devolvió este item

    @Relationship(type = "CON_ITEM_DEVUELTO")
    private Item item;//puede ser nulo cuando se devuelve un pedido

    // normalmente será la misma descripcion que el BienServicioCargado ya que es a partir de este que se devuelve
    // pero se puede editar
    private String descripcionDelBienServicio;

    private String detalles;//detalles de la devolucion producto dañado, faltan algunas cosas, etc.

    private Double cantidad;
    private Double precioAcordadoDevolver;

    private Double montoDevuelto;//el monto total devuelto por la cantidad de este Item. (precioAcordadoDevolver * cantidad)

    private String motivo;

    private Boolean isDineroDevuelto = false;// true (devuelto) false (no devuelto) para encontrar facilmente que dinero faltan devolver en caja.

    private Instant createdAt= Instant.now();

}
