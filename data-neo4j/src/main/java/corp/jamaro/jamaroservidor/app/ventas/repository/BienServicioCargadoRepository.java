package corp.jamaro.jamaroservidor.app.ventas.repository;

import corp.jamaro.jamaroservidor.app.ventas.model.BienServicioCargado;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;
@Repository
public interface BienServicioCargadoRepository extends ReactiveNeo4jRepository<BienServicioCargado, UUID> {

    /**
     * Actualiza solo los campos cantidad y montoAcordado de un BienServicioCargado.
     * Este método preserva todas las relaciones existentes del BienServicioCargado al actualizar
     * únicamente los campos especificados sin sobreescribir el nodo completo.
     * 
     * @param bienServicioCargadoId ID del BienServicioCargado a actualizar
     * @param cantidad Nueva cantidad
     * @param montoAcordado Nuevo monto acordado
     * @return Mono<UUID> ID del BienServicioCargado actualizado
     */
    @Query("""
           MATCH (bsc:BienServicioCargado) WHERE bsc.id = $bienServicioCargadoId
           SET bsc.cantidad = $cantidad,
               bsc.montoAcordado = $montoAcordado
           RETURN bsc.id
           """)
    Mono<UUID> updateBienServicioCargadoCantidadAndMontoAcordado(
            @Param("bienServicioCargadoId") UUID bienServicioCargadoId, 
            @Param("cantidad") Double cantidad, 
            @Param("montoAcordado") Double montoAcordado);
}
