package corp.jamaro.jamaroservidor.app.sunat.enums;

/**
 * Estados de ciclo de vida locales y estados reportados por SUNAT.
 * GENERADO/FIRMADO/ENVIADO son locales de la app.
 * ACEPTADO/RECHAZADO/PENDIENTE/OBSERVADO/ANULADO/ERROR alinean con respuestas SUNAT.
 */
public enum EstadoSunat {
    GENERADO,
    FIRMADO,
    ENVIADO,
    ACEP<PERSON>DO,
    REC<PERSON>ZADO,
    PENDIENTE,
    OBSERVADO,
    ANULADO,
    ERROR
}
