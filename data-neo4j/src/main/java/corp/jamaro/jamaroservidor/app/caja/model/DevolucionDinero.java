package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.dinero.model.DineroSalida;
import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class DevolucionDinero {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private Double montoADevolver;//el monto en Soles

    @Relationship(type = "CON_DINERO_DEVUELTO")//aqui podemos ver que user o users devolvieron el dinero ya que dinero tiene ese campo
    private Set<DineroSalida> dineroDevuelto;//es un set ya que puede ser una devolucion mixta yape y efectivo por ejemplo; en un inicio nisiquiera va a existir hasta que se haga la devolucion.

    @Relationship(type = "INICIADO_POR")
    private User iniciadoPor;
    private Instant creadoEl = Instant.now();
    private Instant devueltoEl;

    private Boolean estaDevuelto = false;// true (devuelto) false (no devuelto) para encontrar facilmente que dineros faltan devolver en caja.
}
