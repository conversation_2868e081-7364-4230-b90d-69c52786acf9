package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.NombreGrupo;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface NombreGrupoRepository extends ReactiveNeo4jRepository<NombreGrupo, UUID> {

    /**
     * Busca los nodos NombreGrupo cuyo campo 'nombre' coincide con la expresión regular dada.
     * La expresión regular debe tener la sintaxis compatible con Neo4j.
     *
     * @param regex Expresión regular para filtrar el nombre.
     * @return Un Flux con los NombreGrupo que cumplen el criterio.
     */
    @Query("MATCH (ng:NombreGrupo) WHERE ng.nombre =~ $regex RETURN ng LIMIT 33")
    Flux<NombreGrupo> findNombresGrupoByNombre(@Param("regex") String regex);

    /**
     * Busca los nodos NombreGrupo cuyo campo 'nombre' coincide con la expresión regular dada
     * y que tengan relación con nodos Grupo de tipo categoria.
     *
     * @param regex Expresión regular para filtrar el nombre.
     * @return Un Flux con los NombreGrupo que cumplen el criterio.
     */
    @Query("MATCH (g:Grupo)-[:CON_NOMBRE_GRUPO]->(ng:NombreGrupo) WHERE g.tipo = 'categoria' AND ng.nombre =~ $regex RETURN DISTINCT ng LIMIT 33")
    Flux<NombreGrupo> findNombresGrupoByNombreAndGrupoCategoria(@Param("regex") String regex);

    /**
     * Encuentra y elimina todos los nodos NombreGrupo que no tienen relación con ningún Grupo.
     *
     * @return Un Mono<Void> que indica la finalización de la operación.
     */
    @Query("MATCH (ng:NombreGrupo) WHERE NOT (ng)<-[:CON_NOMBRE_GRUPO]-(:Grupo) DELETE ng")
    Mono<Void> deleteOrphanedNombreGrupos();
}
