package corp.jamaro.jamaroservidor.app.vehiculo.model;

import corp.jamaro.jamaroservidor.app.model.file.relation.ToBucketFileRelation;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class Vehiculo {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "CON_NOMBRE_DE_VEHICULO")
    private Set<VehiculoNombre> nombres;

    @Relationship(type = "CON_VEHICULO_MARCA")
    private VehiculoMarca vehiculoMarca;

    @Relationship(type = "CON_VEHICULO_MODELO")
    private VehiculoModelo vehiculoModelo;//toyota "probox", nissan "sunny", lancer, yaris

    @Relationship(type = "DEL_ANIO")
    private Set<VehiculoAnio> vehiculoAnios;

    @Relationship(type= "CON_MOTOR")
    private VehiculoMotor vehiculoMotor;

    private String cilindrada;// (e.g., 1000, 1500, 1.5, 2.0).

    private String version;//(GL, XLE, Sport, basic, full, etc.).

    private String carroceria;// (sedán, SUV, hatchback, camioneta, etc.).

    private String tipoTraccion;//Tracción delantera, trasera, 4x4, AWD, etc.

    private String transmision;//mecánica, automática, cvt, etc

    @Relationship(type = "CON_FILE")
    private Set<ToBucketFileRelation> files;

    private Instant creadoActualizado;

}
