package corp.jamaro.jamaroservidor.app.sunat;

import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Data
@Node
//un conjunto de Sale que van a ser usados para crear un comprobante
public class SaleConsolidado {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "CONTINE_VENTA")
    private List<Sale> ventas;

    private BigDecimal totalMonto;

    private Instant createdAt = Instant.now();

}
