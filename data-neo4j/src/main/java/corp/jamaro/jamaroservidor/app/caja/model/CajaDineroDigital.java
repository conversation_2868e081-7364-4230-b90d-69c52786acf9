package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.dinero.model.DineroEntrada;
import corp.jamaro.jamaroservidor.app.dinero.model.DineroSalida;
import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class CajaDineroDigital {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String cuentaDigitalAsignada;//detalles de la cuenta digital asignada, número de cuenta, etc.

    //inicio
    @Relationship(type = "ABRE_CAJA_DINERO_DIGITAL")
    private User abiertaPor;//usuario que abre la caja
    private Instant abiertaEl = Instant.now();
    private Double montoInicialDigital; // en soles

    //Cierre digital
    @Relationship(type = "CIERRE_CAJA_DINERO_DIGITAL")
    private User cerradaPor;//usuario que cierra la caja
    private Instant cerradaEl;

    private Double cierreDigitalCalculado; // totalEntradasDigital - totalSalidasDigital + montoInicialDigital
    private Double cierreDigitalDeclarado; // lo que el User que cierra caja informa
    private Double diferenciaCierreDigital;// cierreDigitalCalculado - cierreDigitalDeclarado

    //CAMPOS PARA CALCULO
    private Double totalEntradasDigital;//Suma de DineroEntrada.montoEntrada de todos los DineroEntrada con tipoDeDinero = DIGITAL
    private Double totalSalidasDigital;//Suma de DineroSalida.montoSalida de todos los DineroSalida con tipoDeDinero = DIGITAL

    //Datos para estadísticas extra de interés que no se usan pal cálculo de cierre, pero si para estadísticas.
    private Double pagosVentaContadoDigital;
    private Double pagosVentaCreditoDigital;
    private Double pagosVentaPedidoDigital;
    private Double devolucionesVentaDigital;
    private Double ingresosExtraDigital;
    private Double gastosExtraDigital;
    private Double pagosDineroProgramadoDigital;

    @Relationship(type = "CON_ENTRADA_CAJA_DIGITAL")
    private Set<DineroEntrada> entradasDigital;

    @Relationship(type = "CON_SALIDA_CAJA_DIGITAL")
    private Set<DineroSalida> salidasDigital;

    private Boolean estaCerrada = false;
}
