package corp.jamaro.jamaroservidor.app.vehiculo.repository;

import corp.jamaro.jamaroservidor.app.vehiculo.model.Vehiculo;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface VehiculoRepository extends ReactiveNeo4jRepository<Vehiculo, UUID> {

    @Query("""
        MATCH (vn:VehiculoNombre)-[:CON_NOMBRE_DE_VEHICULO]-(v:Vehiculo)
        WHERE vn.id = $nombreVehiculoId
        RETURN v
        """)
    Mono<Vehiculo> findVehiculoByNombreVehiculoId(@Param("nombreVehiculoId") UUID nombreVehiculoId);

    @Query("""
        MATCH (v:Vehiculo)
        WHERE v.cilindrada IS NOT NULL AND v.cilindrada =~ $regex
        RETURN DISTINCT v.cilindrada
        LIMIT 20
        """)
    Flux<String> findDistinctCilindradaByRegex(@Param("regex") String regex);

    @Query("""
        MATCH (v:Vehiculo)
        WHERE v.version IS NOT NULL AND v.version =~ $regex
        RETURN DISTINCT v.version
        LIMIT 20
        """)
    Flux<String> findDistinctVersionByRegex(@Param("regex") String regex);

    @Query("""
        MATCH (v:Vehiculo)
        WHERE v.carroceria IS NOT NULL AND v.carroceria =~ $regex
        RETURN DISTINCT v.carroceria
        LIMIT 20
        """)
    Flux<String> findDistinctCarroceriaByRegex(@Param("regex") String regex);

    @Query("""
        MATCH (v:Vehiculo)
        WHERE v.tipoTraccion IS NOT NULL AND v.tipoTraccion =~ $regex
        RETURN DISTINCT v.tipoTraccion
        LIMIT 20
        """)
    Flux<String> findDistinctTipoTraccionByRegex(@Param("regex") String regex);

    @Query("""
        MATCH (v:Vehiculo)
        WHERE v.transmision IS NOT NULL AND v.transmision =~ $regex
        RETURN DISTINCT v.transmision
        LIMIT 20
        """)
    Flux<String> findDistinctTransmisionByRegex(@Param("regex") String regex);

}
