package corp.jamaro.jamaroservidor.app.caja.repository.gui;

import corp.jamaro.jamaroservidor.app.caja.model.gui.CajaGui;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.util.UUID;

@Repository
public interface CajaGuiRepository extends ReactiveNeo4jRepository<CajaGui, UUID> {

    /**
     * Obtiene todos los IDs de CajaGui ordenados por fecha de creación (más recientes primero).
     * Consulta simple sin relaciones para mejor rendimiento.
     *
     * @return Flux que emite los UUIDs de las CajaGui ordenadas por createdAt descendente
     */
    @Query("""
           MATCH (c:CajaGui)
           RETURN c.id AS id
           ORDER BY c.createdAt DESC
           """)
    Flux<UUID> findAllIdsOrderedByCreatedAtDesc();





}
