package corp.jamaro.jamaroservidor.app.ventas.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * DTO con los campos esenciales de SaleGui.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaleGuiDto {

    private UUID id;
    private String iniciadaPor;

    private List<ConnectedUserDto> usersConnected;

    // IDs de SearchProductGui ordenados por createdAt (asc)
    private List<UUID> searchProductIds;

    // ID del Sale (CON_VENTA) que es UUID
    private UUID saleId;

    // ID del CollaborativeRoom (CON_COLLABORATIVE_ROOM) que es UUID
    private UUID collaborativeRoomId;

    private Instant createdAt;

    /**
     * Sub-DTO para exponer datos relevantes de los usuarios conectados.
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ConnectedUserDto {
        private String username;
        private Boolean isOnline;
        private Instant lastConnectionAt;
        private Instant lastDisconnectionAt;
        private String userGuiPreferences; // JSON o simple string
    }
}
