package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.Filtro;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;
@Repository
public interface FiltroRepository extends ReactiveNeo4jRepository<Filtro, UUID> {

    /**
     * Encuentra y elimina todos los nodos Filtro que no tienen relación con ningún Grupo
     * y tampoco tienen relación con ningún Atributo.
     *
     * @return Un Mono<Void> que indica la finalización de la operación.
     */
    @Query("MATCH (f:Filtro) WHERE NOT (f)<-[:CON_FILTRO]-(:Grupo) AND NOT (f)<-[:PERTENECE_AL_FILTRO]-(:Atributo) DELETE f")
    Mono<Void> deleteOrphanedFiltros();
}
