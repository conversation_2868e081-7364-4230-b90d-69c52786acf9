package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.Grupo;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface GrupoRepository extends ReactiveNeo4jRepository<Grupo, String> {

    /**
     * Busca el Grupo que tiene asociado un NombreGrupo con el id especificado.
     *
     * @param nombreGrupoId UUID del NombreGrupo.
     * @return Un Mono con el Grupo encontrado o vacío si no existe.
     */
    @Query("MATCH (g:Grupo)-[:CON_NOMBRE_GRUPO]->(ng:NombreGrupo {id: $nombreGrupoId}) RETURN g")
    Mono<Grupo> findGrupoByNombreGrupoId(@Param("nombreGrupoId") UUID nombreGrupoId);



    /**
     * Elimina las relaciones CON_NOMBRE_GRUPO entre un Grupo y los NombreGrupo especificados,
     * así como los nodos NombreGrupo ya que quedarían huérfanos.
     *
     * @param grupoId ID del Grupo.
     * @param nombreGrupoIds Lista de UUIDs de los NombreGrupo que se eliminarán completamente.
     * @return Un Mono<Void> que indica la finalización de la operación.
     */
    @Query("MATCH (g:Grupo {id: $grupoId})-[r:CON_NOMBRE_GRUPO]->(ng:NombreGrupo) WHERE ng.id IN $nombreGrupoIds DELETE r, ng")
    Mono<Void> deleteNombreGrupoRelations(@Param("grupoId") String grupoId, @Param("nombreGrupoIds") java.util.List<UUID> nombreGrupoIds);

    /**
     * Elimina las relaciones CON_FILTRO entre un Grupo y los Filtros especificados.
     *
     * @param grupoId ID del Grupo.
     * @param filtroIds Lista de UUIDs de los Filtros cuyas relaciones se eliminarán.
     * @return Un Mono<Void> que indica la finalización de la operación.
     */
    @Query("MATCH (g:Grupo {id: $grupoId})-[r:CON_FILTRO]->(f:Filtro) WHERE f.id IN $filtroIds DELETE r")
    Mono<Void> deleteFiltroRelations(@Param("grupoId") String grupoId, @Param("filtroIds") java.util.List<UUID> filtroIds);

}
