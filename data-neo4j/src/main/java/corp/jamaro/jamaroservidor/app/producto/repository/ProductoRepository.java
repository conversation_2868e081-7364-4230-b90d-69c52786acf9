package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.Producto;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import java.util.List;
import java.util.UUID;

public interface ProductoRepository extends ReactiveNeo4jRepository<Producto, UUID> {

    // Consulta personalizada que retorna los primeros 30 productos con sus atributos, filtros, vehículos y códigos de fábrica.
    @Query("""
            MATCH (p:Producto)
            OPTIONAL MATCH (p)-[r:PRODUCTO_CON_ATRIBUTO]->(a:Atributo)
            OPTIONAL MATCH (a)-[rf:PERTENECE_AL_FILTRO]->(f:Filtro)
            OPTIONAL MATCH (p)-[rv:PRODUCTO_PARA_EL_VEHICULO]->(v:Vehiculo)
            OPTIONAL MATCH (v)-[rvn:CON_NOMBRE_DE_VEHICULO]->(vn:VehiculoNombre)
            OPTIONAL MATCH (p)-[rcf:PRODUCTO_CON_CODIGO_FABRICA]->(cf:CodigoFabrica)
            RETURN p, collect(r), collect(a), collect(rf), collect(f),
                   collect(rv), collect(v), collect(rvn), collect(vn),
                   collect(rcf), collect(cf)
            LIMIT 30
            """)
    Flux<Producto> findFirst30();


    // Nueva consulta para buscar sugerencias de descripción con filtro opcional de grupo
    @Query("MATCH (p:Producto) " +
            "WHERE p.descripcion =~ $descripcion " +
            "AND ($idGrupo IS NULL OR $idGrupo = '' OR EXISTS((p)-[:PRODUCTO_PERTENECE_AL_GRUPO]->(:Grupo {id: $idGrupo}))) " +
            "RETURN p.descripcion LIMIT 30")
    Flux<String> findDescriptionsByRegexAndGrupo(@Param("descripcion") String descripcion,
                                                 @Param("idGrupo") String idGrupo);

    // Consulta personalizada para buscar productos por IDs con sus atributos, filtros, vehículos y códigos de fábrica
    @Query("""
            MATCH (p:Producto)
            WHERE p.id IN $ids
            OPTIONAL MATCH (p)-[r:PRODUCTO_CON_ATRIBUTO]->(a:Atributo)
            OPTIONAL MATCH (a)-[rf:PERTENECE_AL_FILTRO]->(f:Filtro)
            OPTIONAL MATCH (p)-[rv:PRODUCTO_PARA_EL_VEHICULO]->(v:Vehiculo)
            OPTIONAL MATCH (v)-[rvn:CON_NOMBRE_DE_VEHICULO]->(vn:VehiculoNombre)
            OPTIONAL MATCH (p)-[rcf:PRODUCTO_CON_CODIGO_FABRICA]->(cf:CodigoFabrica)
            RETURN p, collect(r), collect(a), collect(rf), collect(f),
                   collect(rv), collect(v), collect(rvn), collect(vn),
                   collect(rcf), collect(cf)
            """)
    Flux<Producto> findProductosByIds(@Param("ids") List<String> ids);

    // Derived Query Method - Spring Data Neo4j automatically loads the complete model with all relationships
    Flux<Producto> findByAtributosId(String atributoId);

    // Derived Query Method - Spring Data Neo4j automatically loads the complete model with all relationships
    Flux<Producto> findByCodigosFabricaId(UUID codigoFabricaId);

    // Derived Query Method - Spring Data Neo4j automatically loads the complete model with all relationships
    Mono<Producto> findByCodProductoOld(String codProductoOld);

    // Consulta personalizada para obtener el máximo codProductoOld numérico
    @Query("MATCH (p:Producto) WHERE p.codProductoOld IS NOT NULL AND p.codProductoOld <> '' " +
           "WITH p.codProductoOld AS cod " +
           "WHERE cod =~ '^[0-9]+$' " +
           "RETURN toInteger(cod) AS maxCod ORDER BY maxCod DESC LIMIT 1")
    Mono<Integer> findMaxCodProductoOldAsInteger();


}
