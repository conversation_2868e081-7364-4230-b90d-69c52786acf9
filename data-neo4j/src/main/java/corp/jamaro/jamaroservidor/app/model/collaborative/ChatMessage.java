package corp.jamaro.jamaroservidor.app.model.collaborative;

import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.model.file.relation.ToBucketFileRelation;
import lombok.Data;
import org.springframework.data.annotation.Version;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class ChatMessage {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "BY_USER")
    private User user;  // User que envía el mensaje
    private Instant sentAt = Instant.now();     // Momento en que se envió
    private String content;     // Texto del mensaje
// aplciarlo luego
//    @Relationship(type = "WITH_FILE")
//    private ToBucketFileRelation attachFile;

}
