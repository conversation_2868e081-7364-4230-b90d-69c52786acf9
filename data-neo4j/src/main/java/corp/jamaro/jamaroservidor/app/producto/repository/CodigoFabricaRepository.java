package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.CodigoFabrica;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import reactor.core.publisher.Flux;

import java.util.UUID;

public interface CodigoFabricaRepository extends ReactiveNeo4jRepository<CodigoFabrica, UUID> {

    /**
     * Busca CodigoFabrica por su campo codigo usando una expresión regular.
     * Utilizado para búsquedas que contengan cualquier coincidencia.
     *
     * @param codigoRegex Expresión regular para buscar en el campo codigo.
     * @return Flux de CodigoFabrica que coinciden con la expresión regular.
     */
    @Query("MATCH (cf:CodigoFabrica) WHERE cf.codigo =~ $codigoRegex RETURN cf")
    Flux<CodigoFabrica> findByCodigoRegex(@Param("codigoRegex") String codigoRegex);

}