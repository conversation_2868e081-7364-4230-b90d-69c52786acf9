package corp.jamaro.jamaroservidor.app.model.collaborative;

import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.*;

@Node
@Data
public class CollaborativeRoom {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    @Relationship(type = "INICIADA_POR")
    private User iniciadaPor;

    @Relationship(type = "CON_USUARIO_CONECTADO")
    private Set<UserConnected> usersConnected = new HashSet<>();

    @Relationship(type = "TIENE_MENSAJE")
    private List<ChatMessage> chatMessages = new ArrayList<>();

}
