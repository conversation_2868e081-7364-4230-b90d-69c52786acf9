package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.dinero.model.DineroEntrada;
import corp.jamaro.jamaroservidor.app.dinero.model.DineroSalida;
import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class GastoExtraDinero {

    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String motivo;
    private Double monto;
    
    @Relationship(type = "PROGRAMADO_POR")
    private User programadoPor;

    private TipoDeGasto tipoDeGasto;
    private String detalles;

    private Instant realizadoEl = Instant.now();

    @Relationship(type = "CON_DINERO_GASTADO")
    private Set<DineroSalida> dineroGastado;//las salidas de caja cuando se gasta dinero

    //con dinero devuelto a caja
    @Relationship(type = "DEVUELTO_A_CAJA")
    private Set<DineroEntrada> dineroDevuelto;// A veces sobra dinero del gasto y vuelve a caja

    private Boolean salioDeCaja = false;//para saber si ya fue entregado el dinero para el gasto.

    public enum TipoDeGasto {
        PROVEEDOR,
        FLETE,
        TIENDA,
        TRAMITES,
        MERCADO,
        PAPA,
        MAMA,
        OTROS
    }


}
