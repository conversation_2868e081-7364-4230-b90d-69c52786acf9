package corp.jamaro.jamaroservidor.app.model.task;

import corp.jamaro.jamaroservidor.app.model.file.BucketFile;
import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.model.enums.PriorityEnum;
import corp.jamaro.jamaroservidor.app.model.enums.TaskStatusEnum;
import corp.jamaro.jamaroservidor.app.model.enums.TipoTaskEnum;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Tarea especializada para subir archivos a un bucket (MinIO, S3, etc.).
 * Hereda los campos de la clase base {@link Task} y añade la referencia a {@link BucketFile}.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Node
public class UploadFileTask extends Task {

    /**
     * Campo específico para almacenar información del archivo a subir.
     */
    @Relationship(type = "REFERENCES_FILE")
    private BucketFile bucketFile;


    /**
     * Constructor con Builder que incluye tanto los campos de la clase padre (Task)
     * como el nuevo campo 'bucketFile'.
     */
    @Builder
    public UploadFileTask(
            UUID id,
            String title,
            String descripcion,
            Instant creadoActualizado,
            Instant dueDate,
            TaskStatusEnum estado,
            PriorityEnum prioridad,
            float progress,
            TipoTaskEnum tipo,
            double peso,
            List<User> assignedUsers,
            User assignedBy,
            List<Task> subTasks,
            BucketFile bucketFile
    ) {
        // Invoca el constructor de la clase padre para inicializar sus campos
        super(
                id,
                title,
                descripcion,
                creadoActualizado,
                dueDate,
                estado,
                prioridad,
                progress,
                tipo,
                peso,
                assignedUsers,
                assignedBy,
                subTasks
        );
        // Inicializa el campo específico de esta subclase
        this.bucketFile = bucketFile;
    }
}
