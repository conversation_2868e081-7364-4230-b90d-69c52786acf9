package corp.jamaro.jamaroservidor.app.model.file.relation;

import corp.jamaro.jamaroservidor.app.model.file.BucketFile;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.RelationshipId;
import org.springframework.data.neo4j.core.schema.RelationshipProperties;
import org.springframework.data.neo4j.core.schema.TargetNode;

import java.util.UUID;

@Data
@RelationshipProperties
public class ToBucketFileRelation {
    @RelationshipId
    @Id
    private String id;

    private String fileType;//video, imagen, pdf, etc.

    private Boolean isThumbnail;

    private Integer orden;//orden de presentación.

    @TargetNode
    private BucketFile bucketFile;

}