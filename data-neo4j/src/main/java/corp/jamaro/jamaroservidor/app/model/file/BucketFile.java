package corp.jamaro.jamaroservidor.app.model.file;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.time.Instant;
import java.util.UUID;

@Data
@Node
public class BucketFile {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;
    private String objectName; // Nombre único del objeto dentro del bucket
    private String contentType;// Tipo MIME del objeto, por ejemplo, "image/jpeg"
    private long size; // Tamaño del objeto en bytes
    private String etag; // Identificador único del contenido del objeto
    private Instant creadoActualizado = Instant.now();
    private String metadata;
    private String presignedUrl; // Nuevo campo

}
