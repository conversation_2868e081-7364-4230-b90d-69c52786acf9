package corp.jamaro.jamaroservidor.app.sunat;

import corp.jamaro.jamaroservidor.app.sunat.enums.TipoDocumento;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.UUID;

@Data
@Node
public class DocumentCounter {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;
    private String codigoGenerado;//"serie"+"lastNumber" unique en la base de datos

    private TipoDocumento tipoDocumento;// BOLETA, FACTURA, GUIA_REMISION, NOTA_CREDITO, NOTA_DEBITO, PROFORMA
    private String serie;// ej. B001, F001, P001, etc.
    private Long lastNumber;// el ultimo numero generado para esta serie.
// ayuda a Obtener el siguiente número para una serie y emisor de forma atómica

    public void generateCodigoGenerado() {
        this.codigoGenerado = this.serie + this.lastNumber;
    }
}
/*
Lógica de generación de número
Cuando vayas a crear una boleta:

Buscar el DocumentCounter para tipo "BOLETA" y serie "B001".

Incrementar ultimoNumero en 1.

Guardar el DocumentCounter actualizado.

Crear la Documento con ese número.

Esto asegura que incluso con varios usuarios simultáneos, si la actualización del contador se hace en una transacción (o con un MATCH ... SET ultimoNumero = ultimoNumero + 1 RETURN ultimoNumero en Cypher), no se repitan números.
 */