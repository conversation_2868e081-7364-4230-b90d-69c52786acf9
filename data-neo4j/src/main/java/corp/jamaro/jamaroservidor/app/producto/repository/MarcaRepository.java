package corp.jamaro.jamaroservidor.app.producto.repository;

import corp.jamaro.jamaroservidor.app.producto.model.Marca;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface MarcaRepository extends ReactiveNeo4jRepository<Marca, String> {

    /**
     * Busca Marcas por su campo nombre usando una expresión regular.
     * Utiliza una consulta personalizada para aplicar el regex al campo nombre.
     *
     * @param nombreRegex Expresión regular para buscar en el campo nombre
     * @return Flux de Marcas que coinciden con el criterio de búsqueda
     */
    @Query("MATCH (m:Marca) WHERE m.nombre =~ $nombreRegex RETURN m")
    Flux<Marca> findByNombreRegex(@Param("nombreRegex") String nombreRegex);
}