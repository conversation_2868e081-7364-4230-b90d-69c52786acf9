package corp.jamaro.jamaroservidor.app.producto.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.RelationshipId;
import org.springframework.data.neo4j.core.schema.RelationshipProperties;
import org.springframework.data.neo4j.core.schema.TargetNode;

@Data
@RelationshipProperties
public class GrupoFiltroRelation {

    @RelationshipId
    @Id
    private String id;

    private Integer orden; // Este campo define el orden en el que se mostrará el atributo en la UI.

    @TargetNode
    private Filtro filtro;
}