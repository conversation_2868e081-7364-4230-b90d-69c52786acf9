package corp.jamaro.jamaroservidor.app.producto.model;

import corp.jamaro.jamaroservidor.app.model.file.relation.ToBucketFileRelation;
import corp.jamaro.jamaroservidor.app.vehiculo.model.Vehiculo;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class Producto {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String codProductoOld;
    private String descripcion;

    @Relationship(type = "PRODUCTO_PERTENECE_AL_GRUPO")
    private Set<Grupo> grupos;

    @Relationship(type = "PRODUCTO_CON_ATRIBUTO")
    private Set<Atributo> atributos;

    @Relationship(type = "PRODUCTO_PARA_EL_VEHICULO")
    private Set<Vehiculo> vehiculos;

    @Relationship(type = "PRODUCTO_CON_CODIGO_FABRICA")
    private Set<CodigoFabrica> codigosFabrica;

    @Relationship(type = "CON_FILE")
    private Set<ToBucketFileRelation> files;

    private Instant createdAt= Instant.now();
}
