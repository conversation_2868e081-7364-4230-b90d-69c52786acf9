package corp.jamaro.jamaroservidor.app.caja.model;

import corp.jamaro.jamaroservidor.app.dinero.model.DineroSalida;
import corp.jamaro.jamaroservidor.app.model.User;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
@Node
public class PagoDineroProgramado {//Pago a proveedores trabajadores etc.
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;
    private String motivo;
    private Double monto;
    
    @Relationship(type = "PROGRAMADO_POR")
    private User programadoPor;

    private String detalles;

    private Instant creadoEl = Instant.now();
    private Instant fechaLimite;

    @Relationship(type = "CON_DINERO_PAGADO")
    private Set<DineroSalida> dineroPagado;// aquí se puede ver quien recibe el dinero y más detalles que tiene el model dinero

    private Boolean estaPagado = false;

}
