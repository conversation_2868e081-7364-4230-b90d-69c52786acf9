MySQL:3306/sisjamaro3/categoria/		http://localhost/phpmyadmin/index.php?route=/table/sql&db=sisjamaro3&table=categoria
Su consulta se ejecutó con éxito.

DESCRIBE Categoria;



IdCategoria	int	NO	PRI	NULL	auto_increment	
NombreCategoria	varchar(36)	NO	UNI	NULL		
abreviacion	varchar(255)	NO	UNI	NULL		
NombreAtributoA	varchar(30)	NO		NULL		
NombreAtributoB	varchar(30)	YES		NULL		
NombreAtributoC	varchar(30)	YES		NULL		
NombreAtributoD	varchar(30)	YES		NULL		
NombreAtributoE	varchar(30)	YES		NULL		
EstadoAtributoA	tinyint(1)	NO		NULL		
EstadoAtributoB	tinyint(1)	NO		NULL		
EstadoAtributoC	tinyint(1)	NO		NULL		
EstadoAtributoD	tinyint(1)	NO		NULL		
EstadoAtributoE	tinyint(1)	NO		NULL		



MySQL:3306/sisjamaro3/marca/		http://localhost/phpmyadmin/index.php?route=/table/sql&db=sisjamaro3&table=marca
Su consulta se ejecutó con éxito.

describe marca;



IdMarca	int	NO	PRI	NULL	auto_increment	
Nombre	varchar(45)	NO	UNI	NULL		
Abreviacion	char(3)	NO		NULL		
Origen	varchar(27)	NO		NULL		




MySQL:3306/sisjamaro3/		http://localhost/phpmyadmin/index.php?route=/database/sql&db=sisjamaro3
Su consulta se ejecutó con éxito.

describe producto;



IdProducto	int	NO	PRI	NULL	auto_increment	
IdCategoria	int	NO	MUL	NULL		
CodProducto	varchar(255)	YES	MUL	NULL		
CodCompuesto	varchar(255)	YES		NULL		
CodFabrica	varchar(255)	YES		NULL		
descripcion	varchar(255)	YES		NULL		
Vehiculo	varchar(210)	YES		NULL		
IdMarca	int	NO	MUL	NULL		
VecesPedidas	int	YES		NULL		
AtributoA	varchar(120)	YES		NULL		
AtributoB	varchar(120)	YES		NULL		
AtributoC	varchar(120)	YES		NULL		
AtributoD	varchar(120)	YES		NULL		
AtributoE	varchar(120)	YES		NULL		
Stock	float	YES		NULL		
StockMinimo	float	YES		NULL		
PrecioCompra	float	NO		NULL		
PrecioVenta	float	NO		NULL		
Ubicacion	varchar(13)	NO		NULL		
Anotaciones	text	YES		NULL		
Estado	tinyint(1)	NO		NULL		
seleccion	tinyint(1)	NO		0		




MySQL:3306/sisjamaro3/		http://localhost/phpmyadmin/index.php?route=/database/sql&db=sisjamaro3
Su consulta se ejecutó con éxito.

describe cliente;



IdCliente	int	NO	PRI	NULL	auto_increment	
NombreORazon	varchar(180)	NO		NULL		
DNI	varchar(8)	YES	UNI	NULL		
RUC	varchar(11)	YES	UNI	NULL		
Telefono	varchar(90)	YES		NULL		
Direccion	varchar(270)	YES		NULL		
Email	varchar(90)	YES		NULL		
Estado	tinyint(1)	NO		NULL		
