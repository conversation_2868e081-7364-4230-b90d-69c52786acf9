# Documentación del CajaCobrosService

## Descripción General

El `CajaCobrosService` es el servicio principal encargado de gestionar todos los cobros de dinero en el sistema de caja de Jamaro. Este servicio maneja los pagos tanto en efectivo como digitales, y se integra con el sistema de ventas para procesar cobros de diferentes tipos de venta.

## Funcionalidades Principales

### 1. Procesamiento de Cobros (`procesarCobro`)

**Propósito**: Procesa un cobro para un `CobroDineroProgramado` específico, permitiendo pagos mixtos (efectivo + digital).

**Parámetros**:
- `cajaGuiId`: ID de la caja donde se realiza el cobro
- `cobroDineroProgramadoId`: ID del cobro programado a procesar
- `montoEfectivo`: Cantidad pagada en efectivo (puede ser 0)
- `montoDigital`: Cantidad pagada digitalmente (puede ser 0)
- `detallesEfectivo`: Información adicional del pago en efectivo
- `detallesDigital`: Información adicional del pago digital

**Proceso**:
1. **Validación**: Verifica que al menos uno de los montos sea mayor a 0
2. **Obtención de datos**: Recupera la información de la caja, cobro programado y venta asociada
3. **Verificación de estado**: Confirma que la caja esté abierta y el cobro no esté completado
4. **Validación de montos**: Verifica que el monto total no exceda lo pendiente por cobrar
5. **Creación de registros de dinero**: Genera registros `Dinero` para efectivo y/o digital
6. **Actualización de cajas**: Actualiza los totales de las cajas de efectivo y digital
7. **Actualización del cobro**: Modifica el `CobroDineroProgramado` con el nuevo monto restante
8. **Verificación de completitud**: Si el cobro se completa totalmente, marca como cobrado y actualiza la venta

### 2. Consulta de Cobros Pendientes

El servicio proporciona tres métodos para obtener cobros pendientes según el tipo de venta:

#### `obtenerCobrosPendientesContado()`
- Retorna cobros pendientes para ventas al contado
- Filtra por `estaCobrado = false` y `tipoVenta = CONTADO`

#### `obtenerCobrosPendientesCredito()`
- Retorna cobros pendientes para ventas a crédito
- Filtra por `estaCobrado = false` y `tipoVenta = CREDITO`

#### `obtenerCobrosPendientesPedido()`
- Retorna cobros pendientes para ventas de pedido
- Filtra por `estaCobrado = false` y `tipoVenta = PEDIDO`

## Componentes Internos

### Métodos de Creación de Dinero

#### `crearDineroEfectivo()`
Crea un registro de dinero para pagos en efectivo:
- Establece `tipoDeDinero = EFECTIVO`
- Marca como `esEntrada = true` (ingreso de dinero)
- Asigna el monto directamente a `montoReal` (sistema en soles únicamente)

#### `crearDineroDigital()`
Crea un registro de dinero para pagos digitales:
- Establece `tipoDeDinero = DIGITAL`
- Marca como `esEntrada = true` (ingreso de dinero)
- Asigna el monto directamente a `montoReal` (sistema en soles únicamente)

### Métodos de Actualización de Cajas

#### `actualizarCajaEfectivo()`
Actualiza los totales de la caja de dinero en efectivo:
- Incrementa `totalEntradasEfectivo`
- Actualiza estadísticas específicas según el tipo de venta:
  - `pagosVentaContadoEfectivo` para ventas al contado
  - `pagosVentaCreditoEfectivo` para ventas a crédito
  - `pagosVentaPedidoEfectivo` para ventas de pedido
- Establece relación con el registro de dinero creado

#### `actualizarCajaDigital()`
Actualiza los totales de la caja de dinero digital:
- Incrementa `totalEntradasDigital`
- Actualiza estadísticas específicas según el tipo de venta:
  - `pagosVentaContadoDigital` para ventas al contado
  - `pagosVentaCreditoDigital` para ventas a crédito
  - `pagosVentaPedidoDigital` para ventas de pedido
- Establece relación con el registro de dinero creado

## Integración con Otros Componentes

### Repositorios Utilizados
- `CobroDineroProgramadoRepository`: Gestión de cobros programados
- `DineroRepository`: Persistencia de registros de dinero
- `SaleRepository`: Consulta y actualización de ventas
- `CajaGuiRepository`: Gestión de interfaces de caja
- `CajaEfectivoRepository`: Actualización de cajas de efectivo
- `CajaDigitalRepository`: Actualización de cajas digitales

### Servicios Dependientes
- `CajaGuiService`: Para operaciones relacionadas con la interfaz de caja

## Manejo de Errores

El servicio implementa un manejo robusto de errores:
- **Validación de parámetros**: Verifica que los montos sean válidos
- **Verificación de estado**: Confirma que las entidades estén en estado correcto
- **Validación de montos**: Evita sobrepagos
- **Transacciones**: Utiliza `TransactionalOperator` para garantizar consistencia
- **Logging**: Registra información detallada para debugging

## Características del Sistema de Moneda

**Importante**: El sistema ha sido simplificado para trabajar únicamente con **soles peruanos**:
- No hay conversión de monedas
- Todos los montos se manejan directamente en soles
- Se eliminaron los campos de conversión (`tipoDeMoneda`, `montoAntesDelCambio`, `factorDeCambio`)
- El campo `montoReal` contiene directamente el valor en soles

## Flujo de Datos Típico

1. **Inicio**: El usuario selecciona un cobro pendiente desde la interfaz
2. **Entrada**: Se especifican los montos en efectivo y/o digital
3. **Validación**: El sistema verifica que los datos sean correctos
4. **Procesamiento**: Se crean los registros de dinero y se actualizan las cajas
5. **Finalización**: Se actualiza el estado del cobro y la venta asociada
6. **Resultado**: Se retorna el resultado del procesamiento al usuario

## Consideraciones de Rendimiento

- Utiliza programación reactiva con `Mono` y `Flux` para operaciones asíncronas
- Implementa transacciones para garantizar consistencia de datos
- Optimiza consultas a base de datos mediante repositorios especializados
- Maneja errores de forma no bloqueante

## Logging y Debugging

El servicio incluye logging detallado con prefijo `[DEBUG_LOG]` para facilitar el debugging:
- Registro de parámetros de entrada
- Seguimiento del flujo de procesamiento
- Información de errores con contexto
- Confirmación de operaciones exitosas