plugins {
    id 'java-library'
    id 'io.spring.dependency-management' version '1.1.6' // For dependency management
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.boot:spring-boot-dependencies:3.4.0"
    }
}

group = 'corp.jamaro' // Or a more specific group for the module if preferred
version = '0.0.1-SNAPSHOT' // Or a specific version for the module

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    api 'org.springframework.boot:spring-boot-starter-data-neo4j' // Changed to api
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    // Add other dependencies previously in the main build.gradle that are ONLY used by data-neo4j components
}

// If this module will not be a Spring Boot application itself, remove spring-boot plugin and related configurations if they cause issues.
// The key is to ensure it has the necessary dependencies to compile its code.
