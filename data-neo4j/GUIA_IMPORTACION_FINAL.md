# Guía Definitiva de Importación Neo4j - Sistema Jamaro

Esta es la guía definitiva para la migración de datos desde MySQL hacia Neo4j 5.6. Contiene únicamente los procedimientos correctos y probados, eliminando enfoques obsoletos o problemáticos.

## Tabla de Contenidos

1. [Visión General del Sistema](#visión-general-del-sistema)
2. [Modelo de Datos Neo4j](#modelo-de-datos-neo4j)
3. [Preparación del Entorno](#preparación-del-entorno)
4. [Proceso de Importación](#proceso-de-importación)
5. [Verificación y Validación](#verificación-y-validación)
6. [Solución de Problemas](#solución-de-problemas)
7. [Mejores Prácticas](#mejores-prácticas)

## Visión General del Sistema

### Objetivo
Migrar el sistema de inventario de repuestos automotrices desde una base de datos MySQL relacional hacia Neo4j, aprovechando las capacidades de grafos para mejorar las consultas de filtrado y búsqueda de productos.

### Arquitectura de Datos
El sistema maneja:
- **Productos**: Conceptos abstractos del catálogo (ej: "Filtro de aceite para Toyota")
- **Items**: Instancias físicas específicas con stock y precios (ej: "Filtro Toyota marca X en estante A1")
- **Atributos**: Características técnicas de los productos (diámetro, altura, tipo, etc.)
- **Filtros**: Definiciones de tipos de atributos por categoría
- **Grupos**: Categorías de productos (RODAJE, RETEN, AMORTIGUADOR, etc.)

### Principio Fundamental
**Los Items heredan las características de su Producto padre**. Esta es la base de la arquitectura y la clave para una importación exitosa.

## Modelo de Datos Neo4j

### Nodos Principales

#### Grupo
Representa las categorías de productos del sistema anterior.
```
Propiedades:
- id: String (abreviación de la categoría, ej: "RODA", "RETN")
- tipo: String ("categoria")
- idCategoriaOld: Integer (referencia temporal para migración)
```

#### NombreGrupo
Almacena los nombres descriptivos de cada grupo.
```
Propiedades:
- id: UUID
- nombre: String (ej: "RODAJE", "RETEN")
- isPrincipal: Boolean
- createdAt: DateTime
```

#### Filtro
Define los tipos de atributos que pueden tener los productos de cada grupo.
```
Propiedades:
- id: UUID
- nombreFiltro: String (ej: "Interior:", "Exterior:", "Tipo:")
- tipo: String ("CADENA_TEXTO")
```

#### Atributo
Contiene los valores específicos de cada filtro.
```
Propiedades:
- id: String (compuesto: filtro.id + ':' + base64(valor))
- dato: String (valor del atributo, ej: "30", "BILLAS", "108")
```

#### Producto
Representa los productos del catálogo.
```
Propiedades:
- id: UUID
- codProductoOld: String (código único del producto)
- descripcion: String
- createdAt: DateTime
```

#### Item
Representa las instancias físicas de los productos.
```
Propiedades:
- id: UUID
- codCompuesto: String (código único del item)
- descripcion: String
- precioCostoPromedio: Double
- precioVentaPublico: Double
- stockTotal: Double
- stockDeSeguridad: Double
- anotacionesOld: String
- createdAt: DateTime
```

#### Marca
Información de las marcas de los productos.
```
Propiedades:
- abreviacion: String (ID único)
- nombre: String
- origen: String
- idMarcaOld: Integer (referencia temporal)
```

### Relaciones Principales

- `(Grupo)-[:CON_NOMBRE_GRUPO]->(NombreGrupo)`
- `(Grupo)-[:CON_FILTRO {orden: Integer}]->(Filtro)`
- `(Atributo)-[:PERTENECE_AL_FILTRO]->(Filtro)`
- `(Producto)-[:PRODUCTO_PERTENECE_AL_GRUPO]->(Grupo)`
- `(Producto)-[:PRODUCTO_CON_ATRIBUTO]->(Atributo)`
- `(Item)-[:ITEM_PARTE_DEL_PRODUCTO]->(Producto)`
- `(Item)-[:CON_ATRIBUTO]->(Atributo)` ← **Heredada del Producto**
- `(Item)-[:CON_MARCA]->(Marca)`

### Lógica de Validación de Atributos

**Principio Clave**: Los Grupos controlan qué Atributos pueden existir.

Un Atributo solo se crea si:
1. El campo AtributoX en el CSV tiene un valor
2. El Grupo correspondiente tiene un Filtro con el orden apropiado (1-5)

Ejemplo:
- Si un Producto tiene `AtributoD = "BILLAS"` pero su Grupo no tiene Filtro con `orden: 4` → NO se crea el Atributo
- Si el Grupo sí tiene el Filtro → Se crea el Atributo y las relaciones correspondientes

## Preparación del Entorno

### Prerrequisitos
- Neo4j 5.6 instalado y ejecutándose
- Plugin APOC instalado y habilitado
- Archivos CSV en la carpeta `import` de Neo4j:
  - `categoria.csv`
  - `marca.csv`
  - `producto.csv`

### Estructura de Archivos CSV

#### categoria.csv
```
IdCategoria,NombreCategoria,abreviacion,NombreAtributoA,NombreAtributoB,NombreAtributoC,NombreAtributoD,NombreAtributoE,EstadoAtributoA,EstadoAtributoB,EstadoAtributoC,EstadoAtributoD,EstadoAtributoE
1,RODAJE,RODA,Interior:,Exterior:,Altura:,Tipo:,Aplicación:,1,1,1,1,1
2,RETEN,RETN,interior:,exterior 1:,altura 1:,exterior 2:,altura 2:,1,1,1,1,1
```

#### marca.csv
```
IdMarca,Nombre,Abreviacion,Origen
1,NISSAN,NIS,JAPON
2,SKF,SKF,SUECIA
```

#### producto.csv
```
IdProducto,IdCategoria,CodProducto,CodCompuesto,CodFabrica,descripcion,Vehiculo,IdMarca,VecesPedidas,AtributoA,AtributoB,AtributoC,AtributoD,AtributoE,Stock,StockMinimo,PrecioCompra,PrecioVenta,Ubicacion,Anotaciones,Estado,seleccion
390,1,00197,RODA00197ALT,6206 2RS,RODAJE 6206,,22,0,30,62,16,BILLAS,,0,1,1.6,2.86,E  -P  -S   ,6206 2RS,1,0
```

## Proceso de Importación

### Paso 1: Crear Restricciones Únicas

**¿Por qué primero?** Las restricciones deben existir antes de importar datos para evitar conflictos y garantizar integridad.

```cypher
// Restricciones para nodos principales
CREATE CONSTRAINT grupo_id_unique IF NOT EXISTS FOR (g:Grupo) REQUIRE g.id IS UNIQUE;
CREATE CONSTRAINT nombre_grupo_id_unique IF NOT EXISTS FOR (ng:NombreGrupo) REQUIRE ng.id IS UNIQUE;
CREATE CONSTRAINT nombre_grupo_nombre_unique IF NOT EXISTS FOR (ng:NombreGrupo) REQUIRE ng.nombre IS UNIQUE;
CREATE CONSTRAINT filtro_id_unique IF NOT EXISTS FOR (f:Filtro) REQUIRE f.id IS UNIQUE;
CREATE CONSTRAINT atributo_id_unique IF NOT EXISTS FOR (a:Atributo) REQUIRE a.id IS UNIQUE;
CREATE CONSTRAINT producto_id_unique IF NOT EXISTS FOR (p:Producto) REQUIRE p.id IS UNIQUE;
CREATE CONSTRAINT producto_codProductoOld_unique IF NOT EXISTS FOR (p:Producto) REQUIRE p.codProductoOld IS UNIQUE;
CREATE CONSTRAINT item_id_unique IF NOT EXISTS FOR (i:Item) REQUIRE i.id IS UNIQUE;
CREATE CONSTRAINT item_codCompuesto_unique IF NOT EXISTS FOR (i:Item) REQUIRE i.codCompuesto IS UNIQUE;
CREATE CONSTRAINT marca_abreviacion_unique IF NOT EXISTS FOR (m:Marca) REQUIRE m.abreviacion IS UNIQUE;
CREATE CONSTRAINT codigoFabrica_codigo_unique IF NOT EXISTS FOR (cf:CodigoFabrica) REQUIRE cf.codigo IS UNIQUE;
```

### Paso 2: Importar Grupos y Filtros

**Lógica**: Crear la estructura de categorías y sus filtros permitidos basándose en la configuración de `EstadoAtributoX`.

```cypher
LOAD CSV WITH HEADERS FROM "file:///categoria.csv" AS row

// Crear nodo Grupo
CREATE (g:Grupo {
    id: row.abreviacion,
    tipo: 'categoria',
    idCategoriaOld: toInteger(row.IdCategoria)
})

// Crear nodo NombreGrupo y relación
CREATE (ng:NombreGrupo {
    id: apoc.create.uuid(),
    nombre: row.NombreCategoria,
    isPrincipal: true,
    createdAt: datetime()
})
CREATE (g)-[:CON_NOMBRE_GRUPO]->(ng)

// Crear Filtros condicionalmente basándose en EstadoAtributoX = 1
FOREACH (_ IN CASE WHEN row.NombreAtributoA IS NOT NULL AND row.NombreAtributoA <> '' AND toInteger(row.EstadoAtributoA) = 1 THEN [1] ELSE [] END |
    CREATE (f:Filtro {
        id: apoc.create.uuid(), 
        nombreFiltro: row.NombreAtributoA,
        tipo: 'CADENA_TEXTO'
    })
    CREATE (g)-[:CON_FILTRO {orden: 1}]->(f)
)
FOREACH (_ IN CASE WHEN row.NombreAtributoB IS NOT NULL AND row.NombreAtributoB <> '' AND toInteger(row.EstadoAtributoB) = 1 THEN [1] ELSE [] END |
    CREATE (f:Filtro {
        id: apoc.create.uuid(), 
        nombreFiltro: row.NombreAtributoB,
        tipo: 'CADENA_TEXTO'
    })
    CREATE (g)-[:CON_FILTRO {orden: 2}]->(f)
)
FOREACH (_ IN CASE WHEN row.NombreAtributoC IS NOT NULL AND row.NombreAtributoC <> '' AND toInteger(row.EstadoAtributoC) = 1 THEN [1] ELSE [] END |
    CREATE (f:Filtro {
        id: apoc.create.uuid(), 
        nombreFiltro: row.NombreAtributoC,
        tipo: 'CADENA_TEXTO'
    })
    CREATE (g)-[:CON_FILTRO {orden: 3}]->(f)
)
FOREACH (_ IN CASE WHEN row.NombreAtributoD IS NOT NULL AND row.NombreAtributoD <> '' AND toInteger(row.EstadoAtributoD) = 1 THEN [1] ELSE [] END |
    CREATE (f:Filtro {
        id: apoc.create.uuid(), 
        nombreFiltro: row.NombreAtributoD,
        tipo: 'CADENA_TEXTO'
    })
    CREATE (g)-[:CON_FILTRO {orden: 4}]->(f)
)
FOREACH (_ IN CASE WHEN row.NombreAtributoE IS NOT NULL AND row.NombreAtributoE <> '' AND toInteger(row.EstadoAtributoE) = 1 THEN [1] ELSE [] END |
    CREATE (f:Filtro {
        id: apoc.create.uuid(), 
        nombreFiltro: row.NombreAtributoE,
        tipo: 'CADENA_TEXTO'
    })
    CREATE (g)-[:CON_FILTRO {orden: 5}]->(f)
);
```

### Paso 3: Importar Marcas

**Lógica**: Crear nodos Marca con deduplicación por IdMarca.

```cypher
LOAD CSV WITH HEADERS FROM "file:///marca.csv" AS row
WITH row
WHERE row.IdMarca IS NOT NULL AND row.IdMarca <> ''
  AND row.Nombre IS NOT NULL AND row.Nombre <> ''
  AND row.Abreviacion IS NOT NULL AND row.Abreviacion <> ''
  // CORREGIDO: Removido filtro de Origen para permitir valores vacíos

// Deduplicación: agrupar por IdMarca y tomar la primera ocurrencia
WITH row.IdMarca AS idMarca, collect(row) AS rows
WITH idMarca, rows[0] AS row

CREATE (m:Marca {
    abreviacion: toLower(row.Abreviacion),
    nombre: row.Nombre,
    origen: CASE WHEN row.Origen IS NULL OR row.Origen = '' THEN 'NO_ESPECIFICADO' ELSE row.Origen END,
    idMarcaOld: toInteger(row.IdMarca)
});
```

### Paso 4: Importar Productos con Atributos

**Lógica Clave**: 
- Agrupar por `CodProducto` (múltiples filas del CSV pueden tener el mismo CodProducto)
- Usar solo la primera fila de cada grupo para crear el Producto y sus Atributos
- Crear Atributos usando el enfoque CALL + OPTIONAL MATCH (confiable)

```cypher
LOAD CSV WITH HEADERS FROM "file:///producto.csv" AS row
WITH row
WHERE row.CodProducto IS NOT NULL AND row.CodProducto <> ''
  AND row.Estado IS NOT NULL AND row.Estado = '1'

// Agrupar por CodProducto - CRÍTICO: Solo usar la primera fila
WITH row.CodProducto AS codProducto, collect(row) AS rows
WITH codProducto, rows[0] AS row

// Crear nodo Producto
CREATE (p:Producto {
    id: apoc.create.uuid(),
    codProductoOld: row.CodProducto,
    descripcion: row.descripcion,
    createdAt: datetime()
})

// Asociar con Grupo
WITH p, row
MATCH (g:Grupo {idCategoriaOld: toInteger(row.IdCategoria)})
CREATE (p)-[:PRODUCTO_PERTENECE_AL_GRUPO]->(g)

// Crear relaciones con Atributos usando enfoque confiable
WITH p, row, g

// AtributoA
CALL {
    WITH p, row, g
    WITH p, row, g WHERE row.AtributoA IS NOT NULL AND row.AtributoA <> ''
    OPTIONAL MATCH (g)-[:CON_FILTRO {orden: 1}]->(f:Filtro)
    WITH p, row, g, f WHERE f IS NOT NULL
    MERGE (a:Atributo {id: f.id + ':' + apoc.text.base64Encode(toLower(trim(row.AtributoA)))})
    ON CREATE SET a.dato = trim(row.AtributoA)
    MERGE (a)-[:PERTENECE_AL_FILTRO]->(f)
    MERGE (p)-[:PRODUCTO_CON_ATRIBUTO]->(a)
}

// AtributoB
CALL {
    WITH p, row, g
    WITH p, row, g WHERE row.AtributoB IS NOT NULL AND row.AtributoB <> ''
    OPTIONAL MATCH (g)-[:CON_FILTRO {orden: 2}]->(f:Filtro)
    WITH p, row, g, f WHERE f IS NOT NULL
    MERGE (a:Atributo {id: f.id + ':' + apoc.text.base64Encode(toLower(trim(row.AtributoB)))})
    ON CREATE SET a.dato = trim(row.AtributoB)
    MERGE (a)-[:PERTENECE_AL_FILTRO]->(f)
    MERGE (p)-[:PRODUCTO_CON_ATRIBUTO]->(a)
}

// AtributoC
CALL {
    WITH p, row, g
    WITH p, row, g WHERE row.AtributoC IS NOT NULL AND row.AtributoC <> ''
    OPTIONAL MATCH (g)-[:CON_FILTRO {orden: 3}]->(f:Filtro)
    WITH p, row, g, f WHERE f IS NOT NULL
    MERGE (a:Atributo {id: f.id + ':' + apoc.text.base64Encode(toLower(trim(row.AtributoC)))})
    ON CREATE SET a.dato = trim(row.AtributoC)
    MERGE (a)-[:PERTENECE_AL_FILTRO]->(f)
    MERGE (p)-[:PRODUCTO_CON_ATRIBUTO]->(a)
}

// AtributoD
CALL {
    WITH p, row, g
    WITH p, row, g WHERE row.AtributoD IS NOT NULL AND row.AtributoD <> ''
    OPTIONAL MATCH (g)-[:CON_FILTRO {orden: 4}]->(f:Filtro)
    WITH p, row, g, f WHERE f IS NOT NULL
    MERGE (a:Atributo {id: f.id + ':' + apoc.text.base64Encode(toLower(trim(row.AtributoD)))})
    ON CREATE SET a.dato = trim(row.AtributoD)
    MERGE (a)-[:PERTENECE_AL_FILTRO]->(f)
    MERGE (p)-[:PRODUCTO_CON_ATRIBUTO]->(a)
}

// AtributoE
CALL {
    WITH p, row, g
    WITH p, row, g WHERE row.AtributoE IS NOT NULL AND row.AtributoE <> ''
    OPTIONAL MATCH (g)-[:CON_FILTRO {orden: 5}]->(f:Filtro)
    WITH p, row, g, f WHERE f IS NOT NULL
    MERGE (a:Atributo {id: f.id + ':' + apoc.text.base64Encode(toLower(trim(row.AtributoE)))})
    ON CREATE SET a.dato = trim(row.AtributoE)
    MERGE (a)-[:PERTENECE_AL_FILTRO]->(f)
    MERGE (p)-[:PRODUCTO_CON_ATRIBUTO]->(a)
}

// Crear relaciones con Vehículos (si aplica)
FOREACH (ignore IN CASE WHEN row.Vehiculo IS NOT NULL AND row.Vehiculo <> '' THEN [1] ELSE [] END |
    MERGE (vn:VehiculoNombre {nombre: row.Vehiculo})
    ON CREATE SET vn.id = apoc.create.uuid(),
                  vn.isPrincipal = true,
                  vn.creadoEl = datetime()
    MERGE (v:Vehiculo)-[:CON_NOMBRE_DE_VEHICULO]->(vn)
    ON CREATE SET v.id = apoc.create.uuid(),
                  v.creadoActualizado = datetime()
    MERGE (p)-[:PRODUCTO_PARA_EL_VEHICULO]->(v)
)

// Crear relaciones con CodigoFabrica (si aplica)
FOREACH (ignore IN CASE WHEN row.CodFabrica IS NOT NULL AND row.CodFabrica <> '' THEN [1] ELSE [] END |
    MERGE (cf:CodigoFabrica {codigo: row.CodFabrica})
    ON CREATE SET cf.id = apoc.create.uuid()
    MERGE (p)-[:PRODUCTO_CON_CODIGO_FABRICA]->(cf)
);
```

### Paso 5: Importar Items (Enfoque de Herencia)

**Enfoque Definitivo**: Los Items heredan los Atributos de su Producto padre. Este es el enfoque más confiable y alineado con la lógica de negocio.

#### Paso 5.1: Crear nodos Item y relaciones básicas

```cypher
LOAD CSV WITH HEADERS FROM "file:///producto.csv" AS pm
WITH pm
WHERE pm.CodCompuesto IS NOT NULL AND pm.CodCompuesto <> ''
  AND size(pm.CodCompuesto) > 4

// Agrupar por CodCompuesto (cada uno es único)
WITH pm.CodCompuesto AS codCompuesto, collect(pm) AS rows
WITH codCompuesto, rows[0] AS pm

// Crear nodo Item con validación del código
WITH pm, substring(pm.CodCompuesto, 4) AS codigoLimpio
WHERE codigoLimpio <> ''

CREATE (i:Item {
    id: apoc.create.uuid(),
    codCompuesto: codigoLimpio,
    descripcion: pm.descripcion,
    precioCostoPromedio: toFloat(pm.PrecioCompra),
    precioVentaPublico: toFloat(pm.PrecioVenta),
    stockTotal: toFloat(pm.Stock),
    stockDeSeguridad: toFloat(pm.StockMinimo),
    anotacionesOld: pm.Anotaciones,
    createdAt: datetime()
})

// Relación con Producto
WITH i, pm
MATCH (p:Producto {codProductoOld: pm.CodProducto})
CREATE (i)-[:ITEM_PARTE_DEL_PRODUCTO]->(p)

// Relación con Marca
WITH i, pm, p
MATCH (m:Marca {idMarcaOld: toInteger(pm.IdMarca)})
CREATE (i)-[:CON_MARCA]->(m)

// Relación con CodigoFabrica (si aplica)
WITH i, pm, p
FOREACH (ignore IN CASE WHEN pm.CodFabrica IS NOT NULL AND pm.CodFabrica <> '' THEN [1] ELSE [] END |
    MERGE (cf:CodigoFabrica {codigo: pm.CodFabrica})
    ON CREATE SET cf.id = apoc.create.uuid()
    CREATE (i)-[:CON_CODIGO_FABRICA]->(cf)
);
```

#### Paso 5.2: Heredar Atributos del Producto padre

**Esta es la clave del éxito**: En lugar de intentar recrear los Atributos, los Items simplemente heredan las relaciones de su Producto padre.

```cypher
// HERENCIA DE ATRIBUTOS - Enfoque definitivo y confiable
MATCH (i:Item)-[:ITEM_PARTE_DEL_PRODUCTO]->(p:Producto)
WHERE NOT EXISTS((i)-[:CON_ATRIBUTO]->())  // Solo procesar Items sin Atributos

// Copiar todas las relaciones Atributo del Producto al Item
WITH i, p
MATCH (p)-[:PRODUCTO_CON_ATRIBUTO]->(a:Atributo)
CREATE (i)-[:CON_ATRIBUTO]->(a);
```

### Paso 6: Crear Índices de Búsqueda

**Propósito**: Optimizar las consultas de búsqueda y filtrado.

```cypher
// Índices de texto para búsquedas
CREATE TEXT INDEX producto_descripcion_text IF NOT EXISTS
FOR (p:Producto) ON (p.descripcion);

CREATE TEXT INDEX item_descripcion_text IF NOT EXISTS
FOR (i:Item) ON (i.descripcion);

CREATE TEXT INDEX atributo_dato_text IF NOT EXISTS
FOR (a:Atributo) ON (a.dato);

CREATE TEXT INDEX nombreGrupo_nombre_text IF NOT EXISTS
FOR (ng:NombreGrupo) ON (ng.nombre);

CREATE TEXT INDEX filtro_nombreFiltro_text IF NOT EXISTS
FOR (f:Filtro) ON (f.nombreFiltro);
```

### Paso 7: Limpieza de Campos Temporales

**Propósito**: Eliminar campos que solo se usaron durante la migración.

```cypher
// Eliminar campos temporales
MATCH (m:Marca)
REMOVE m.idMarcaOld;

MATCH (g:Grupo)
REMOVE g.idCategoriaOld;
```

## Verificación y Validación

### Conteos Básicos

```cypher
// Verificar que todos los nodos se crearon
MATCH (g:Grupo) RETURN count(g) as TotalGrupos;
MATCH (ng:NombreGrupo) RETURN count(ng) as TotalNombresGrupo;
MATCH (f:Filtro) RETURN count(f) as TotalFiltros;
MATCH (m:Marca) RETURN count(m) as TotalMarcas;
MATCH (p:Producto) RETURN count(p) as TotalProductos;
MATCH (i:Item) RETURN count(i) as TotalItems;
MATCH (a:Atributo) RETURN count(a) as TotalAtributos;
```

### Verificación de Relaciones

```cypher
// Verificar que los Productos tienen Atributos
MATCH (p:Producto)-[:PRODUCTO_CON_ATRIBUTO]->(a:Atributo)
RETURN count(*) as ProductosConAtributos;

// Verificar que los Items tienen Atributos (herencia funcionó)
MATCH (i:Item)-[:CON_ATRIBUTO]->(a:Atributo)
RETURN count(*) as ItemsConAtributos;

// Verificar que Items tienen los mismos Atributos que su Producto
MATCH (i:Item)-[:ITEM_PARTE_DEL_PRODUCTO]->(p:Producto)
WITH i, p, 
     [(i)-[:CON_ATRIBUTO]->(a:Atributo) | a.dato] as itemAtributos,
     [(p)-[:PRODUCTO_CON_ATRIBUTO]->(a:Atributo) | a.dato] as productoAtributos
WHERE size(itemAtributos) <> size(productoAtributos)
RETURN count(*) as ItemsConAtributosDiferentes; // Debe ser 0
```

### Verificación de Integridad

```cypher
// Verificar que no hay Atributos huérfanos
MATCH (a:Atributo)
WHERE NOT (a)-[:PERTENECE_AL_FILTRO]->()
RETURN count(a) as AtributosHuerfanos; // Debe ser 0

// Verificar que no hay Items sin Atributos
MATCH (i:Item)
WHERE NOT (i)-[:CON_ATRIBUTO]->()
RETURN count(i) as ItemsSinAtributos; // Debe ser 0

// Verificar que no hay Productos sin Atributos
MATCH (p:Producto)
WHERE NOT (p)-[:PRODUCTO_CON_ATRIBUTO]->()
RETURN count(p) as ProductosSinAtributos;
```

### Verificación de Casos Específicos

```cypher
// Verificar un caso específico de herencia
MATCH (i:Item {codCompuesto: '00197ALT'})-[:ITEM_PARTE_DEL_PRODUCTO]->(p:Producto)
MATCH (i)-[:CON_ATRIBUTO]->(a:Atributo)
MATCH (p)-[:PRODUCTO_CON_ATRIBUTO]->(a2:Atributo)
RETURN i.codCompuesto, p.codProductoOld, 
       collect(DISTINCT a.dato) as ItemAtributos,
       collect(DISTINCT a2.dato) as ProductoAtributos;
```

## Solución de Problemas

### Problema: Items sin Atributos

**Síntoma**: La consulta `MATCH (i:Item) WHERE NOT (i)-[:CON_ATRIBUTO]->() RETURN count(i)` devuelve > 0

**Causa**: El paso de herencia no se ejecutó correctamente

**Solución**: Re-ejecutar el paso 5.2 de herencia de Atributos

### Problema: Atributos Duplicados

**Síntoma**: Múltiples Atributos con el mismo dato pero diferentes IDs

**Causa**: Problemas en la normalización de espacios en blanco

**Solución**: Verificar que se usa `trim()` en la generación de IDs de Atributo

### Problema: Productos sin Atributos

**Síntoma**: Productos que deberían tener Atributos pero no los tienen

**Causa**: El Grupo correspondiente no tiene los Filtros necesarios

**Solución**: 
1. Verificar que `EstadoAtributoX = 1` en el CSV de categorías
2. Re-ejecutar el paso 2 de importación de Grupos y Filtros

### Problema: Marcas no se crean (FBJ, MBA, etc.)

**Síntoma**: Algunos nodos Marca no se crean a pesar de estar en el CSV, especialmente aquellos con campos Origen vacíos

**Causa**: El filtro `WHERE row.Origen IS NOT NULL AND row.Origen <> ''` excluye filas con campos Origen vacíos

**Ejemplo de datos problemáticos**:
```
"24","FBJ","FBJ",     <- Campo Origen vacío
"25","MBA","MBA",     <- Campo Origen vacío
```

**Solución**: Usar la consulta corregida que maneja campos Origen vacíos:
```cypher
LOAD CSV WITH HEADERS FROM "file:///marca.csv" AS row
WITH row
WHERE row.IdMarca IS NOT NULL AND row.IdMarca <> ''
  AND row.Nombre IS NOT NULL AND row.Nombre <> ''
  AND row.Abreviacion IS NOT NULL AND row.Abreviacion <> ''
  // NO filtrar por Origen

CREATE (m:Marca {
    abreviacion: toLower(row.Abreviacion),
    nombre: row.Nombre,
    origen: CASE WHEN row.Origen IS NULL OR row.Origen = '' THEN 'NO_ESPECIFICADO' ELSE row.Origen END,
    idMarcaOld: toInteger(row.IdMarca)
});
```

### Problema: Constraint Violations

**Síntoma**: Errores de restricción única durante la importación

**Causa**: Datos duplicados en los CSVs o ejecución múltiple de las consultas

**Solución**: 
1. Limpiar la base de datos: `MATCH (n) DETACH DELETE n`
2. Re-ejecutar desde el paso 1

## Mejores Prácticas

### Para Desarrolladores

1. **Siempre ejecutar en orden**: Los pasos tienen dependencias críticas
2. **Verificar después de cada paso**: No continuar si hay errores
3. **Usar transacciones**: Para grandes volúmenes, considerar `USING PERIODIC COMMIT`
4. **Backup antes de importar**: Siempre tener un respaldo

### Para Mantenimiento

1. **Documentar cambios**: Cualquier modificación al modelo debe documentarse
2. **Probar en desarrollo**: Nunca ejecutar directamente en producción
3. **Monitorear performance**: Los índices pueden necesitar ajustes según el uso

### Para Extensiones Futuras

1. **Usar el patrón de herencia**: Para nuevos tipos de entidades que deban heredar propiedades
2. **Mantener la validación por Filtros**: No crear Atributos sin Filtros correspondientes
3. **Considerar la normalización**: Espacios en blanco y caracteres especiales

## Orden de Ejecución Obligatorio

1. ✅ **Restricciones únicas** (Paso 1)
2. ✅ **Grupos y Filtros** (Paso 2)
3. ✅ **Marcas** (Paso 3)
4. ✅ **Productos con Atributos** (Paso 4)
5. ✅ **Items - Nodos básicos** (Paso 5.1)
6. ✅ **Items - Herencia de Atributos** (Paso 5.2)
7. ✅ **Índices de búsqueda** (Paso 6)
8. ✅ **Limpieza** (Paso 7)
9. ✅ **Verificación completa** (Sección Verificación)

## Conclusión

Esta guía representa la solución definitiva para la migración de datos del sistema Jamaro hacia Neo4j. El enfoque de herencia para Items es la clave del éxito, eliminando la complejidad innecesaria y garantizando la consistencia de datos.

**Principios Fundamentales Aplicados**:
- Simplicidad sobre complejidad
- Herencia sobre recreación
- Validación por diseño
- Verificación exhaustiva

La implementación de estos principios garantiza una migración exitosa y un sistema mantenible para el futuro.