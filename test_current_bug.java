// Test script to demonstrate the current bug in CajaCobrosService
// This simulates the scenario described in the issue:
// - Bill is 200
// - Customer pays 300
// - System correctly calculates change as 100
// - But incorrectly sets montoReal to 300 instead of 200

class TestCurrentBug {
    public static void main(String[] args) {
        System.out.println("=== TESTING CURRENT BUG IN CajaCobrosService ===");
        
        // Simulate the current problematic logic
        double montoACobrar = 200.0;  // Amount owed
        double montoRecibido = 300.0; // Amount received
        
        System.out.println("Scenario:");
        System.out.println("- Amount owed (montoACobrar): " + montoACobrar);
        System.out.println("- Amount received (montoRecibido): " + montoRecibido);
        
        // Current logic in CajaCobrosService.procesarCobroInterno()
        double totalPagado = montoRecibido;
        double montoRestante = montoACobrar;
        double vuelto = Math.max(0, totalPagado - montoRestante);
        double montoAplicado = Math.min(totalPagado, montoRestante);
        
        System.out.println("\nCurrent logic results:");
        System.out.println("- Change (vuelto): " + vuelto + " ✓ CORRECT");
        System.out.println("- Applied amount (montoAplicado): " + montoAplicado + " ✓ CORRECT");
        
        // BUG: In crearDineroEfectivo/crearDineroDigital methods
        // The code sets dinero.setMontoReal(monto) where monto = montoRecibido
        double montoRealIncorrecto = montoRecibido; // This is the BUG
        double montoRealCorrecto = montoAplicado;   // This is what it should be
        
        System.out.println("\nBUG DEMONSTRATION:");
        System.out.println("- Current montoReal (INCORRECT): " + montoRealIncorrecto + " ❌ BUG!");
        System.out.println("- Correct montoReal should be: " + montoRealCorrecto + " ✓");
        
        System.out.println("\nPROBLEM:");
        System.out.println("The Dinero record shows montoReal=" + montoRealIncorrecto + 
                          " but the actual transaction value should be " + montoRealCorrecto);
        System.out.println("This causes accounting errors in cash register calculations.");
        
        System.out.println("\n=== PROPOSED SOLUTION ===");
        System.out.println("Remove change logic entirely:");
        System.out.println("- Client must send exact amounts");
        System.out.println("- No change calculation needed");
        System.out.println("- montoReal = actual payment amount (no overpayment allowed)");
        System.out.println("- Simpler validation: totalPayments must equal amountOwed exactly");
    }
}