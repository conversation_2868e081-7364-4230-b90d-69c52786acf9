spring.application.name=JamaroServidor

# Configuración de Neo4j para tests (usar base de datos embebida o test)
spring.neo4j.uri=bolt://localhost:7687
spring.neo4j.authentication.username=neo4j
spring.neo4j.authentication.password=test

# Configuración de RSocket para tests - usar puerto aleatorio
spring.rsocket.server.port=0
spring.rsocket.server.transport=tcp

# Configuración de JWT para tests
jwt.secret=testSecretKeyForJWTTokensInTestEnvironmentOnly
jwt.expiration=3600

# Configuración de logging para tests
logging.level.root=WARN
logging.level.corp.jamaro=INFO
logging.level.io.rsocket=ERROR
logging.level.io.rsocket.FrameLogger=OFF
logging.level.org.springframework.security=WARN
logging.level.io.jsonwebtoken=WARN
logging.level.org.springframework.rsocket=WARN
logging.level.org.springframework.data.neo4j.cypher.unrecognized=ERROR
logging.level.org.neo4j.driver=ERROR
logging.level.org.neo4j.cypherdsl.core=ERROR

# Configuración de Minio para tests (deshabilitado o mock)
minio.url=http://localhost:9000
minio.access-key=testkey
minio.secret-key=testsecret
minio.bucket=test-bucket