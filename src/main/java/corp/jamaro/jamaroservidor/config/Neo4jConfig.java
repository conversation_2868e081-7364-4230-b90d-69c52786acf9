package corp.jamaro.jamaroservidor.config;

import org.neo4j.cypherdsl.core.renderer.Dialect;
import org.neo4j.driver.Driver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.neo4j.core.Neo4jClient;
import org.springframework.data.neo4j.core.Neo4jTemplate;
import org.springframework.data.neo4j.core.ReactiveDatabaseSelectionProvider;
import org.springframework.data.neo4j.core.ReactiveNeo4jClient;
import org.springframework.data.neo4j.core.ReactiveNeo4jTemplate;
import org.springframework.data.neo4j.core.convert.Neo4jConversions;
import org.springframework.data.neo4j.core.mapping.Neo4jMappingContext;
import org.springframework.data.neo4j.core.transaction.ReactiveNeo4jTransactionManager;
import org.springframework.data.neo4j.repository.config.EnableReactiveNeo4jRepositories;
import org.springframework.data.neo4j.repository.config.ReactiveNeo4jRepositoryConfigurationExtension;
import org.springframework.transaction.ReactiveTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.reactive.TransactionalOperator;

/**
 * Configuración explícita para Neo4j y transacciones reactivas.
 * Esta configuración asegura que el ReactiveTransactionManager esté disponible
 * para los servicios que requieren TransactionalOperator.
 */
@Configuration
@EnableTransactionManagement
@EnableReactiveNeo4jRepositories(basePackages = {"corp.jamaro.jamaroservidor.app", "corp.jamaro.jamaroservidor.security"})
public class Neo4jConfig {

    /**
     * Configure the Cypher-DSL to use Neo4j 5 dialect.
     * This will make Spring Data Neo4j use elementId() instead of id() in queries,
     * eliminating the deprecation warnings.
     */
    @Bean
    public org.neo4j.cypherdsl.core.renderer.Configuration cypherDslConfiguration() {
        return org.neo4j.cypherdsl.core.renderer.Configuration.newConfig()
                .withDialect(Dialect.NEO4J_5)
                .build();
    }

    /**
     * Bean para el ReactiveTransactionManager de Neo4j.
     * Este bean es requerido por TransactionalOperator para manejar transacciones reactivas.
     *
     * @param driver Driver de Neo4j auto-configurado por Spring Boot
     * @return ReactiveTransactionManager configurado para Neo4j
     */
    @Bean
    public ReactiveNeo4jTransactionManager reactiveTransactionManager(Driver driver) {
        return new ReactiveNeo4jTransactionManager(driver);
    }

    /**
     * Bean para TransactionalOperator.
     * Este bean permite a los servicios ejecutar operaciones dentro de transacciones reactivas.
     *
     * @param reactiveTransactionManager ReactiveTransactionManager configurado
     * @return TransactionalOperator listo para usar en servicios
     */
    @Bean
    public TransactionalOperator transactionalOperator(ReactiveTransactionManager reactiveTransactionManager) {
        return TransactionalOperator.create(reactiveTransactionManager);
    }

    @Bean
    public Neo4jMappingContext neo4jMappingContext() {
        return new Neo4jMappingContext(new Neo4jConversions());
    }

    @Bean
    public ReactiveNeo4jClient reactiveNeo4jClient(Driver driver) {
        return ReactiveNeo4jClient.create(driver);
    }

    @Bean(ReactiveNeo4jRepositoryConfigurationExtension.DEFAULT_NEO4J_TEMPLATE_BEAN_NAME)
    public ReactiveNeo4jTemplate reactiveNeo4jTemplate(ReactiveNeo4jClient reactiveNeo4jClient, Neo4jMappingContext neo4jMappingContext) {
        return new ReactiveNeo4jTemplate(reactiveNeo4jClient, neo4jMappingContext);
    }
}
