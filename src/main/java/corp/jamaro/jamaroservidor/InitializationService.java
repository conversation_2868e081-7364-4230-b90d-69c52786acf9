package corp.jamaro.jamaroservidor;

import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.repository.UserRepository;
import corp.jamaro.jamaroservidor.security.model.Role;
import corp.jamaro.jamaroservidor.security.enums.RoleEnum;
import corp.jamaro.jamaroservidor.security.model.UserAuth;
import corp.jamaro.jamaroservidor.security.repository.UserAuthRepository;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class InitializationService {

    private final UserRepository userRepository;
    private final UserAuthRepository userAuthRepository;
    @PostConstruct
    public void initializeData() {
        log.info("Inicializando datos...");

        userAuthRepository.deleteAll()
                .doOnSuccess(unused -> log.info("Todos los nodos User y UserAuth eliminados."))
                .then(createInitialData())
                .doOnSuccess(unused -> log.info("Datos iniciales creados exitosamente."))
                .doOnError(error -> log.error("Error durante la inicialización de datos: {}", error.getMessage()))
                .subscribe();
    }

    private Mono<Void> createInitialData() {
        return Mono.defer(() -> {
            // Crear usuario "josemz" sin roles en el User
            User joseUser = createUser(
                    UUID.fromString("89d339b5-64f1-40e4-835e-29125067f048"), "josemz", "jose", "martinez zanabria", "44853731");
            // Crear UserAuth para "josemz" con rol ADMIN
            UserAuth joseAuth = createUserAuth("josemz", "123456", "jose123", joseUser, RoleEnum.ADMIN);

            // Crear usuario "jamaramz" sin roles en el User
            User jamaraUser = createUser(
                    UUID.fromString("70634141-54e7-4001-9c6b-18b79a0ebf17"), "jamaramz", "jamara", "martinez zanabria", "70691143");
            // Crear UserAuth para "jamaramz" con rol MANAGER
            UserAuth jamaraAuth = createUserAuth("jamaramz", "654321", "jamara123", jamaraUser, RoleEnum.MANAGER);

            //Crear UserAuth con rol SALES_REP
            User vendedorUser = createUser(
                    UUID.fromString("fbfa0b11-7560-47ad-b401-5fba09164857"), "vendedor1", "vendedor1", "vendedor prueba1", "12345678");
            UserAuth vendedorAuth = createUserAuth("vendedor1", "123456789", "vendedor123", vendedorUser, RoleEnum.SALES_REP);

            return userRepository.saveAll(Set.of(joseUser, jamaraUser))
                    .then(userAuthRepository.saveAll(Set.of(joseAuth, jamaraAuth, vendedorAuth)).then());
        });
    }

    private User createUser(UUID id, String username, String nombre, String apellidos, String documento) {
        User user = new User();
        user.setId(id);
        user.setUsername(username);
        user.setNombre(nombre);
        user.setApellidos(apellidos);
        user.setDocumento(documento);
        return user;
    }

    private UserAuth createUserAuth(String username, String password, String rfid, User user, RoleEnum roleEnum) {
        UserAuth userAuth = new UserAuth();
        userAuth.setUsername(username);
        userAuth.setPassword(password);
        userAuth.setRfid(rfid);
        userAuth.setUser(user);

        Set<Role> roles = new HashSet<>();
        roles.add(createRole(roleEnum));
        userAuth.setRoles(roles);

        return userAuth;
    }

    private Role createRole(RoleEnum roleEnum) {
        Role role = new Role();
        role.setRoleName(roleEnum);
        return role;
    }
}
