- trabajar en como se guardan los modelos para no chancar las relaciones ni saltarnos los constraints en VehiculoService

- Hacer una interfaz con las opciones del Sistema por ejemplouna que necesitaremos es eliminar
los SaleGui con su respectivo SearchProductGui y su Sale para los Sale con private Boolean esProforma=true;
entonces necesitamos setear cada cuanto tiempo hacerlo, por ejemplo cada 1 semana.

"""
Mejora semántica del tipo de retorno
Actualmente usas Mono<Boolean> en SaleService, que es totalmente válido.
Pero si en el futuro quieres manejar mejor errores específicos, podrías usar:

public Mono<Either<ErrorTipo, SaleResultado>> iniciarVentaContado(...)
O incluso:
public Mono<ResultStatus>

Esto hace que el cliente sepa por qué falló:
Sin stock, Error de conexión, Usuario no autenticado, etc.

Pero si por ahora solo necesitas saber si salió bien o mal, tu implementación actual es clara y directa. ✅
"""
"""
Debemos crear Lógica para eliminar los SearchProductGui huerfanos que tengan más de 3 días de creado.

"""
"""
derived query methods: no sirve para crear los metodos en los repository de una manera
mas idiomatica ejemplo findByCodProducto, etc; sin necesidad de contruir la query cypher
"""

"""
Despues de actualizar la base de datos eliminar todos los Filtro que no tengan tipo ya que esos son
los antiguos, quiza hacerlo grupo por grupo, en realidad al final tenemos q limpiar varios datos analiza
"""

"""
En vez de eliminar un Modelo de la base de datos o agregar un campo estado podemos cambiarle solo el Label
titulo de nodo por ejemplo de Producto a ProductoInactive
"""

