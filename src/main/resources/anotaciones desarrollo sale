El UniversalSaleGui:
::::::::::::::::::::::::::::::
- el la primera vista que el user obtiene despues de autenticarse
- esta tiene un primer tab principal en dodne carga diferentes herramientas como ( tareas asignada<PERSON>, notas del usuario, entre cualquier otra idea)
- Luego en otro tab carga su MainSaleGUI; solo uno (el Suyo) en caso de ser un User sin permisos de Admin o Manager (el servidor usa Spring Security)
en caso tenga permisos puede ver los MainSaleGui de otros user para auditarlos de ser necesario o para ayudarlos.

- tambien guarda un json con configuraciones de la GUI del usuario como colore de usuario, tamaño de letra entre otras.

::::::::::::::::::::::::::::::

::::::::::::::::::::::::::::::