package corp.jamaro.jamaroservidor.sunat.service;

import io.github.project.openubl.xbuilder.content.models.standard.general.Invoice;
import io.github.project.openubl.xbuilder.content.models.standard.general.CreditNote;
import io.github.project.openubl.xbuilder.content.models.standard.general.DebitNote;
import io.github.project.openubl.xbuilder.content.models.sunat.baja.VoidedDocuments;
import io.github.project.openubl.xbuilder.content.models.sunat.resumen.SummaryDocuments;
import io.github.project.openubl.xbuilder.enricher.ContentEnricher;
import io.github.project.openubl.xbuilder.renderer.TemplateProducer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Adaptador sobre XBuilder que centraliza el renderizado de documentos UBL.
 *
 * - Asegura que el Proveedor (emisor) siempre esté presente usando la config.
 * - <PERSON><PERSON><PERSON> (calcular defaults/tasas/fechas) antes de renderizar.
 * - Uso de tipos concretos de XBuilder para máxima claridad y type-safety.
 */
@Service
@RequiredArgsConstructor
public class XBuilderRenderService {

    private final ContentEnricher enricher;
    private final TemplateProducer templateProducer;
    private final SunatComponentsService componentsService;

    public String renderInvoice(Invoice invoice) {
        if (invoice.getProveedor() == null) {
            invoice.setProveedor(componentsService.buildProveedor());
        }
        enricher.enrich(invoice);
        return templateProducer.getInvoice().data(invoice).render();
    }

    /** Renderiza Nota de crédito. */
    public String renderCreditNote(CreditNote creditNote) {
        if (creditNote.getProveedor() == null) {
            creditNote.setProveedor(componentsService.buildProveedor());
        }
        enricher.enrich(creditNote);
        return templateProducer.getCreditNote().data(creditNote).render();
    }

    /** Renderiza Nota de débito. */
    public String renderDebitNote(DebitNote debitNote) {
        if (debitNote.getProveedor() == null) {
            debitNote.setProveedor(componentsService.buildProveedor());
        }
        enricher.enrich(debitNote);
        return templateProducer.getDebitNote().data(debitNote).render();
    }

    /** Renderiza comunicación de baja (VoidedDocuments). */
    public String renderVoidedDocuments(VoidedDocuments voidedDocuments) {
        if (voidedDocuments.getProveedor() == null) {
            voidedDocuments.setProveedor(componentsService.buildProveedor());
        }
        enricher.enrich(voidedDocuments);
        // Algunas versiones podrían exponer getVoidedDocuments o getVoided
        return renderWithCandidates(voidedDocuments, "getVoidedDocuments", "getVoided");
    }

    /** Renderiza resumen diario. */
    public String renderSummaryDocuments(SummaryDocuments summaryDocuments) {
        if (summaryDocuments.getProveedor() == null) {
            summaryDocuments.setProveedor(componentsService.buildProveedor());
        }
        enricher.enrich(summaryDocuments);
        return renderWithCandidates(summaryDocuments, "getSummaryDocuments");
    }

    /** Renderiza Guía de Remisión (Despatch). */
    public String renderDespatch(Object despatch) {
        ensureProveedor(despatch);
        enrichReflective(despatch);
        // Algunas versiones podrían nombrar el template de forma diferente
        return renderWithCandidates(despatch, "getDespatch", "getDespatchAdvice", "getShipment");
    }

    private void ensureProveedor(Object doc) {
        try {
            var getter = doc.getClass().getMethod("getProveedor");
            Object prov = getter.invoke(doc);
            if (prov == null) {
                var setter = doc.getClass().getMethod("setProveedor", getter.getReturnType());
                setter.invoke(doc, componentsService.buildProveedor());
            }
        } catch (Exception ignored) {
            // Documento sin proveedor explícito
        }
    }

    private void enrichReflective(Object doc) {
        try {
            var m = enricher.getClass().getMethod("enrich", doc.getClass());
            m.invoke(enricher, doc);
        } catch (Exception e) {
            throw new IllegalStateException("No se pudo enriquecer el documento " + doc.getClass().getSimpleName(), e);
        }
    }

    private String renderWithCandidates(Object doc, String... methodCandidates) {
        Exception last = null;
        for (String name : methodCandidates) {
            try {
                var m = templateProducer.getClass().getMethod(name);
                Object template = m.invoke(templateProducer);
                var data = template.getClass().getMethod("data", doc.getClass());
                Object templated = data.invoke(template, doc);
                var render = templated.getClass().getMethod("render");
                return (String) render.invoke(templated);
            } catch (Exception ex) {
                last = ex;
            }
        }
        throw new IllegalStateException("No se pudo renderizar el documento " + doc.getClass().getSimpleName(), last);
    }
}
