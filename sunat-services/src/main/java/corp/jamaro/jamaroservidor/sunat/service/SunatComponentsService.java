package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.config.SunatProperties;
import io.github.project.openubl.xbuilder.content.models.common.Direccion;
import io.github.project.openubl.xbuilder.content.models.common.Proveedor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Construye componentes comunes para los documentos (por ejemplo, el Proveedor/Emisor)
 * a partir de la configuración en application.properties (sunat.company.*).
 */
@Service
@RequiredArgsConstructor
public class SunatComponentsService {

    private final SunatProperties properties;

    /** Devuelve un Proveedor preparado con los datos de la empresa configurada. */
    public Proveedor buildProveedor() {
        SunatProperties.Company c = properties.getCompany();
        Direccion direccion = Direccion.builder()
                .ubigeo(c.getUbigeo())
                .codigoLocal(c.getCodigoLocal())
                .urbanizacion(c.getUrbanizacion())
                .departamento(c.getDepartamento())
                .provincia(c.getProvincia())
                .distrito(c.getDistrito())
                .direccion(c.getDireccion())
                .codigoPais(c.getCodigoPais())
                .build();

        return Proveedor.builder()
                .ruc(c.getRuc())
                .nombreComercial(c.getNombreComercial())
                .razonSocial(c.getRazonSocial())
                .direccion(direccion)
                .build();
    }
}
