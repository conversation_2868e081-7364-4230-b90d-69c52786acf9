package corp.jamaro.jamaroservidor.sunat.config;

import io.github.project.openubl.xbuilder.enricher.ContentEnricher;
import io.github.project.openubl.xbuilder.enricher.config.DateProvider;
import io.github.project.openubl.xbuilder.enricher.config.Defaults;
import io.github.project.openubl.xbuilder.renderer.TemplateProducer;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.time.LocalDate;

@Configuration
@EnableConfigurationProperties(SunatProperties.class)
public class SunatConfiguration {

    @Bean
    public Defaults xbuilderDefaults() {
        // Valores por defecto para el enriquecedor de XBuilder.
        // IGV 18% e ICB 0.50; ajuste estos valores si su negocio lo requiere.
        return Defaults.builder()
                .igvTasa(new BigDecimal("0.18"))
                .icbTasa(new BigDecimal("0.50"))
                .build();
    }

    @Bean
    public DateProvider dateProvider() {
        // Proveedor de fecha para XBuilder (permite pruebas/reproducibilidad si se mockea)
        return () -> LocalDate.now();
    }

    @Bean
    public ContentEnricher contentEnricher(Defaults defaults, DateProvider dateProvider) {
        // Componente que completa valores faltantes/calculados en los modelos de XBuilder
        return new ContentEnricher(defaults, dateProvider);
    }

    @Bean
    public TemplateProducer templateProducer() {
        // Productor de plantillas UBL/Factura/Boleta/Notas para XBuilder
        return TemplateProducer.getInstance();
    }

    // NOTA: Se eliminaron beans de XSender (CompanyURLs, CompanyCredentials)
    // porque este módulo ya no realiza envíos a SUNAT. Solo se mantiene lógica XBuilder.
}
