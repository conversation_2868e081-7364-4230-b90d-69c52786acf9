package corp.jamaro.jamaroservidor.sunat.service;

import corp.jamaro.jamaroservidor.sunat.config.SunatProperties;
import io.github.project.openubl.xbuilder.signature.CertificateDetails;
import io.github.project.openubl.xbuilder.signature.CertificateDetailsFactory;
import io.github.project.openubl.xbuilder.signature.XMLSigner;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;

import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;

/**
 * Firma XMLs UBL usando el certificado indicado en sunat.certificate.*
 *
 * Notas:
 * - La ruta puede ser de classpath (por ejemplo, src/main/resources) o absoluta.
 * - El alias del certificado no es necesario para la API actual de XBuilder.
 */
@Service
@RequiredArgsConstructor
public class XmlSignerService {

    private final SunatProperties properties;

    /**
     * Recibe un XML sin firmar en texto y devuelve el contenido firmado como bytes.
     */
    public byte[] sign(String unsignedXml) throws Exception {
        try (InputStream ksInputStream = getKeystoreInputStream()) {
            if (ksInputStream == null) {
                throw new IllegalStateException("No se pudo cargar el certificado desde: " + properties.getCertificate().getPath());
            }
            CertificateDetails certificateDetails = CertificateDetailsFactory.create(ksInputStream, properties.getCertificate().getPassword());
            X509Certificate certificate = certificateDetails.getX509Certificate();
            PrivateKey privateKey = certificateDetails.getPrivateKey();

            // Firma usando XBuilder XMLSigner con el ID de firma "openubl"
            Document signed = XMLSigner.signXML(unsignedXml, "openubl", certificate, privateKey);
            return toBytes(signed);
        }
    }

    /** Intenta cargar el .p12/.pfx desde el classpath; si no, usa el classloader local. */
    private InputStream getKeystoreInputStream() {
        String path = properties.getCertificate().getPath();
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream(path);
        if (in == null) {
            in = getClass().getClassLoader().getResourceAsStream(path);
        }
        return in;
    }

    /** Convierte el DOM firmado a bytes UTF-8. */
    private byte[] toBytes(Document document) throws Exception {
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer transformer = tf.newTransformer();
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
        transformer.setOutputProperty(OutputKeys.METHOD, "xml");
        transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
        transformer.setOutputProperty(OutputKeys.INDENT, "no");
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        transformer.transform(new DOMSource(document), new StreamResult(baos));
        return baos.toByteArray();
    }
}
