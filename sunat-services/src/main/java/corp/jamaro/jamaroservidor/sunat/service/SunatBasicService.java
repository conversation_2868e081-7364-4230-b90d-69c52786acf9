package corp.jamaro.jamaroservidor.sunat.service;

import io.github.project.openubl.xbuilder.content.models.standard.general.Invoice;
import io.github.project.openubl.xbuilder.content.models.standard.general.CreditNote;
import io.github.project.openubl.xbuilder.content.models.standard.general.DebitNote;
import io.github.project.openubl.xbuilder.content.models.sunat.baja.VoidedDocuments;
import io.github.project.openubl.xbuilder.content.models.sunat.resumen.SummaryDocuments;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Servicio básico enfocado exclusivamente en XBuilder.
 *
 * - Renderiza documentos UBL (Factura, Boleta, Notas, etc.) usando XBuilder.
 * - Firma XMLs usando el certificado configurado.
 *
 * Importante: Este módulo NO realiza envíos a SUNAT (XSender fue eliminado).
 * Si necesita enviar, hágalo desde otro módulo/servicio HTTP/WS externo.
 */
@Service
@RequiredArgsConstructor
public class SunatBasicService {

    private final XBuilderRenderService renderService;
    private final XmlSignerService signerService;

    // ---------------- Render helpers (XBuilder) ----------------

    /**
     * Renderiza una Factura/Boleta a XML sin firmar.
     * Asegura que el Proveedor (emisor) esté presente y enriquece el modelo.
     */
    public String renderInvoice(Invoice invoice) {
        return renderService.renderInvoice(invoice);
    }

    /** Renderiza una Nota de crédito a XML sin firmar. */
    public String renderCreditNote(CreditNote creditNote) {
        return renderService.renderCreditNote(creditNote);
    }

    /** Renderiza una Nota de débito a XML sin firmar. */
    public String renderDebitNote(DebitNote debitNote) {
        return renderService.renderDebitNote(debitNote);
    }

    /** Renderiza una comunicación de baja (VoidedDocuments) a XML sin firmar. */
    public String renderVoidedDocuments(VoidedDocuments voidedDocuments) {
        return renderService.renderVoidedDocuments(voidedDocuments);
    }

    /** Renderiza un Resumen diario a XML sin firmar. */
    public String renderSummaryDocuments(SummaryDocuments summaryDocuments) {
        return renderService.renderSummaryDocuments(summaryDocuments);
    }

    /** Renderiza una Guía de Remisión (Despatch) a XML sin firmar. */
    public String renderDespatch(Object despatch) {
        return renderService.renderDespatch(despatch);
    }

    // ---------------- Sign helpers ----------------

    /**
     * Firma un XML sin firmar y devuelve el contenido firmado como bytes.
     */
    public byte[] signXml(String unsignedXml) throws Exception {
        return signerService.sign(unsignedXml);
    }
}
