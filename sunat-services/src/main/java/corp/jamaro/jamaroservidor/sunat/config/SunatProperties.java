package corp.jamaro.jamaroservidor.sunat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Propiedades de configuración para XBuilder y datos de empresa.
 *
 * Aunque se mantienen algunas URLs históricas de SUNAT, este módulo no envía documentos.
 * Puede reutilizar estos valores desde otro módulo si necesita integrarse con servicios de SUNAT.
 */
@Data
@ConfigurationProperties(prefix = "sunat")
public class SunatProperties {
    private boolean beta = true;
    private Urls urls = new Urls();
    private Company company = new Company();
    private Auth auth = new Auth();
    private Certificate certificate = new Certificate();
    private Output output = new Output();
    private Series series = new Series();
    private Counter counter = new Counter();

    @Data
    public static class Urls {
        /**
         * billService para Factura/Boleta/Notas (beta o producción)
         */
        private String invoice;
        /**
         * billService para Percepciones y Retenciones (beta o producción)
         */
        private String perceptionRetention;
        /**
         * API REST para Guía de Remisión (beta o producción)
         */
        private String despatch;
        /**
         * Servicio de consulta CDR (opcional)
         */
        private String cdr;
    }

    @Data
    public static class Company {
        private String ruc;
        private String razonSocial;
        private String nombreComercial;
        // Dirección plana según properties existentes
        private String direccion;
        private String ubigeo;
        private String departamento;
        private String provincia;
        private String distrito;
        private String urbanizacion;
        private String codigoLocal;
        private String codigoPais = "PE";
    }

    @Data
    public static class Auth {
        private String username;
        private String password;
    }

    @Data
    public static class Certificate {
        /** Ruta dentro del classpath o absoluta */
        private String path;
        private String password;
        /** Alias opcional, si el certificado requiere */
        private String alias;
    }

    @Data
    public static class Output {
        private String xmlFolder;
        private String cdrFolder;
    }

    @Data
    public static class Series {
        private String factura;
        private String boleta;
    }

    @Data
    public static class Counter {
        private int facturaStart;
        private int boletaStart;
    }
}
